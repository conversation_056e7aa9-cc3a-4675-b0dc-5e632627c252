:root {
  /* indigo-600 */
  --color-accent: 79 70 229; /* #4f46e5 */
  /* indigo-300 */
  --color-accent-disabled: 165 180 252; /* #a5b4fc */
  /* indigo-800 */
  --color-accent-hover: 55 48 163; /* #3730a3 */
  /* white */
  --color-accent-text: 255 255 255; /* #ffffff */

  /* gray-900 */
  --color-main: 24 24 27; /* #18181b */
  /* gray-700 */
  --color-main-hover: 63 63 70; /* #3f3f46 */
  /* white */
  --color-main-text: 255 255 255; /* #ffffff */

  /* gray-700 */
  --color-control: 82 82 91; /* #52525b */
  /* gray-900 */
  --color-control-hover: 24 24 27; /* #18181b */

  /* gray-500 */
  --color-control-light: 113 113 122; /* #71717a */
  /* gray-600 */
  --color-control-light-hover: 82 82 91; /* #52525b */

  /* gray-100 */
  --color-control-bg: 243 244 246; /* #f3f4f6 */
  /* gray-200 */
  --color-control-bg-hover: 229 231 235; /* #e5e7eb */

  /* gray-400 */
  --color-control-placeholder: 161 161 170; /* #a1a1aa */

  /* blue-600 */
  --color-info: 37 99 235; /* #2563eb */
  /* blue-700 */
  --color-info-hover: 29 78 216; /* #1d4ed8 */

  /* yellow-500 */
  --color-warning: 245 158 11; /* #f59e0b */
  /* yellow-700 */
  --color-warning-hover: 180 83 9; /* #b45309 */

  /* red-600 */
  --color-error: 220 38 38; /* #dc2626 */
  /* red-700 */
  --color-error-hover: 185 28 28; /* #b91c1c */

  /* green-600 */
  --color-success: 22 163 74; /* #16a34a */
  /* green-700 */
  --color-success-hover: 21 128 61; /* #15803d */

  /* gray-200 */
  --color-link-hover: 229 231 235; /* #e5e7eb */

  /* gray-200 */
  --color-block-border: 229 231 235; /* #e5e7eb */
  /* gray-300 */
  --color-control-border: 209 213 219; /* #d1d5db */
}

/*
  Colors for dark theme
  Only used by Web Terminal now
*/
:root {
  /* same as vs-dark theme's bg, not pure black. */
  --color-dark-bg: 30 30 30; /* #1e1e1e */

  /* The Matrix green */
  --color-matrix-green: 0 204 0; /* #00cc00 */
  /* The matrix green lighter */
  --color-matrix-green-hover: 136 255 136; /* #88ff88 */
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .bb-grid {
    @apply grid border-block-border;
  }
  .bb-grid-row {
    @apply contents;
  }
  .bb-grid-header-cell {
    @apply flex items-center bg-gray-50 text-left text-xs font-medium text-gray-500 tracking-wider;
  }
  .bb-grid:not(.compact) .bb-grid-header-cell {
    @apply px-2 py-2;
    @apply first:pl-4 last:pr-4;
  }
  .bb-grid.compact .bb-grid-header-cell {
    @apply px-1 py-2;
    @apply first:pl-2 last:pr-2;
  }
  .bb-grid-cell {
    @apply border-t border-block-border flex items-center text-sm leading-5 whitespace-pre-wrap break-all;
  }
  .bb-grid:not(.compact) .bb-grid-cell {
    @apply px-2 py-2;
    @apply first:pl-4 last:pr-4;
  }
  .bb-grid.compact .bb-grid-cell {
    @apply px-1 py-2;
    @apply first:pl-2 last:pr-2;
  }
  .bb-grid-row.clickable:hover > .bb-grid-cell {
    @apply cursor-pointer bg-gray-100;
  }
  .bb-grid.hide-header .bb-grid-row:first-child .bb-grid-cell {
    @apply border-t-0;
  }
}

.btn-primary {
  @apply select-none inline-flex border border-transparent rounded text-accent-text bg-accent hover:bg-accent-hover disabled:bg-accent disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-sm leading-5 font-medium focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-normal {
  @apply select-none inline-flex border border-control-border rounded text-control bg-white hover:bg-control-bg-hover disabled:bg-control-bg disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-sm leading-5 font-medium focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-secondary {
  @apply select-none inline-flex items-center text-main hover:text-control-hover disabled:opacity-50 disabled:cursor-not-allowed text-sm leading-5 font-normal focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-small {
  @apply select-none inline-flex border border-control-border rounded text-control bg-white hover:bg-control-bg-hover disabled:bg-control-bg disabled:opacity-50 disabled:cursor-not-allowed px-2 text-xs leading-5 focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-select {
  @apply text-sm select-none text-main bg-white border border-control-border disabled:bg-control-bg disabled:opacity-50 disabled:cursor-not-allowed rounded shadow-sm text-left cursor-default focus:outline-none focus:ring-1 focus:ring-control focus:border-control;
}

.btn-cancel {
  @apply select-none inline-flex text-control rounded hover:bg-control-bg-hover disabled:bg-control-bg disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-sm leading-5 font-medium focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-icon {
  @apply cursor-pointer flex flex-row items-center select-none rounded-full text-control bg-transparent hover:text-control-hover disabled:opacity-50 disabled:cursor-not-allowed focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-1;
}

.btn-icon-primary {
  @apply select-none rounded-full text-white bg-accent hover:bg-accent-hover disabled:opacity-50 disabled:cursor-not-allowed focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-1;
}

.btn-danger {
  @apply select-none inline-flex border border-transparent rounded text-accent-text bg-error hover:bg-error-hover disabled:bg-error disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-sm leading-5 font-medium focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.btn-success {
  @apply select-none inline-flex border border-transparent rounded text-accent-text bg-success hover:bg-success-hover disabled:bg-success disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-sm leading-5 font-medium focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.tab {
  @apply rounded text-control font-normal text-sm focus:ring-control focus:outline-none focus-visible:ring-2 focus:ring-offset-2;
}

.control {
  @apply focus:ring-control focus:border-control border-control-border rounded select-none;
}

.textlabel {
  @apply text-sm font-medium text-control;
}

.textinfolabel {
  @apply text-sm font-normal text-control-light;
}

.textlabeltip {
  @apply text-xs font-normal text-red-500 ml-2;
}

.textfield {
  @apply text-main disabled:text-control disabled:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-70 focus:ring-control focus:border-control sm:text-sm border-control-border rounded;
}

.textarea {
  @apply text-main focus:ring-control focus:border-control sm:text-sm border-control-border shadow-sm;
}

.radio-set-row {
  @apply flex flex-row items-center gap-x-4;
}

.radio-set-col {
  @apply flex flex-col space-y-2;
}

.radio {
  @apply flex items-center;
}

.radio .btn {
  @apply focus:ring-accent h-4 w-4 text-accent select-none;
}

.radio .label {
  @apply ml-1.5 block text-sm font-medium text-gray-700 select-none;
}

.outline-title {
  @apply text-main w-full items-center text-xs leading-5 font-semibold focus:outline-none select-none tracking-wider;
}

.outline-title.toplevel {
  @apply text-control-light text-sm font-normal tracking-tight;
}

.outline-title.collapsible {
  @apply cursor-pointer hover:bg-link-hover;
}

.outline-item {
  @apply text-control cursor-pointer w-full items-center hover:bg-link-hover text-sm leading-5 focus:outline-none select-none;
}

.menu-item {
  @apply cursor-pointer block px-4 py-2 text-sm leading-5 text-control hover:bg-control-bg hover:text-control-hover focus:outline-none focus:bg-control-bg focus:text-control-hover;
}

.bar-link {
  @apply cursor-pointer text-main hover:bg-link-hover focus:outline-none;
}

.accent-link {
  @apply cursor-pointer text-accent font-medium hover:underline focus:outline-none;
}

.normal-link {
  @apply cursor-pointer text-blue-600 hover:underline hover:text-blue-800 focus:outline-none;
}

.light-link {
  @apply cursor-pointer text-blue-400 hover:underline hover:text-blue-200 focus:outline-none;
}

.icon-link {
  @apply cursor-pointer text-control hover:bg-link-hover focus:outline-none;
}

.anchor-link {
  @apply cursor-pointer hover:underline focus:outline-none;
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .thumb-up {
    @apply origin-bottom-left;
    animation: thumb-up 1s ease-in-out infinite;
  }

  /* Remove the ticker in the number field for webkit */
  input.hide-ticker::-webkit-outer-spin-button,
  input.hide-ticker::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Remove the ticker in the number field for Firefox */
  input[type="number"].hide-ticker {
    -moz-appearance: textfield;
  }

  .fix-scrollbar-z-index {
    transform: translate3d(0, 0, 0);
  }

  .writing-vertical-rl {
    writing-mode: vertical-rl;
  }
  .writing-vertical-lr {
    writing-mode: vertical-lr;
  }

  .text-size-adjust-none {
    text-size-adjust: none;
    -webkit-text-size-adjust: none;
    -moz-text-size-adjust: none;
    -ms-text-size-adjust: none;
  }
}

.fade-enter-active,
.fade-leave-active,
.fade-fast-enter-active,
.fade-fast-leave-active,
.fade-slow-enter-active,
.fade-slow-leave-active {
  @apply transition-opacity ease-in-out duration-300;
}

.fade-fast-enter-active,
.fade-fast-leave-active {
  @apply duration-100;
}

.fade-slow-enter-active,
.fade-slow-leave-active {
  @apply duration-500;
}

.fade-enter-from,
.fade-leave-to,
.fade-fast-enter-from,
.fade-fast-leave-to,
.fade-slow-enter-from,
.fade-slow-leave-to {
  @apply opacity-0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  @apply transition-transform ease-in-out duration-150;
}
.slide-up-enter-from,
.slide-up-leave-to {
  @apply translate-y-full;
}

@keyframes thumb-up {
  33% {
    transform: rotate(-15deg);
  }
  50% {
    transform: rotate(5deg);
  }
  66% {
    transform: rotate(0deg);
  }
}

.fade-slide-up-leave-active {
  @apply transition-[opacity,_transform] duration-100 ease-in-out;
}

.fade-slide-up-enter-active {
  @apply transition-[opacity,_transform] duration-100 ease-in-out;
}

.fade-slide-up-enter-from,
.fade-slide-up-leave-to {
  @apply opacity-0 scale-90;
}

.fade-slide-up-leave-from,
.fade-slide-up-enter-to {
  @apply opacity-100 scale-100;
}

/* compatibility fixes for tailwindcss and naive-ui */
.n-base-selection-input:focus,
.n-base-selection-input-tag__input:focus,
.n-input__input-el:focus {
  @apply ring-0;
}
