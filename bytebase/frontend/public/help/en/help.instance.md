---
title: What is 'Instance' ?
---

- Each Bytebase instance belongs to an environment. An instance usually maps to one of your database instance represented by an host:port address. This could be your on-premises MySQL instance or RDS instance.

- Bytebase requires read/write (NOT the super privilege) access to the instance in order to perform database operations on behalf of the user.

#### Learn more

- [Add an Instance](https://www.bytebase.com/docs/get-started/step-by-step/add-an-instance)