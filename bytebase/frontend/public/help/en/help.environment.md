---
title: What is 'Environment' ?
---

- Each environment maps to one of your testing, staging, prod environment respectively.

- Environment is a global setting, one Bytebase deployment only contains a single set of environments.

- Database instances are created under a particular environment.

#### Learn more

- [Set up Environments](https://www.bytebase.com/docs/get-started/step-by-step/set-up-environments)