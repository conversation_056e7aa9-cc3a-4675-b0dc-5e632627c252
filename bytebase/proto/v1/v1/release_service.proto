syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/sql_service.proto";

option go_package = "generated-go/v1";

service ReleaseService {
  rpc GetRelease(GetReleaseRequest) returns (Release) {
    option (google.api.http) = {get: "/v1/{name=projects/*/releases/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.releases.get";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc ListReleases(ListReleasesRequest) returns (ListReleasesResponse) {
    option (google.api.http) = {get: "/v1/{parent=projects/*}/releases"};
    option (google.api.method_signature) = "parent";
    option (bytebase.v1.permission) = "bb.releases.list";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc CreateRelease(CreateReleaseRequest) returns (Release) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/releases"
      body: "release"
    };
    option (google.api.method_signature) = "parent,release";
    option (bytebase.v1.permission) = "bb.releases.create";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc UpdateRelease(UpdateReleaseRequest) returns (Release) {
    option (google.api.http) = {
      patch: "/v1/{release.name=projects/*/releases/*}"
      body: "release"
    };
    option (google.api.method_signature) = "release,update_mask";
    option (bytebase.v1.permission) = "bb.releases.update";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc DeleteRelease(DeleteReleaseRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=projects/*/releases/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.releases.delete";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc UndeleteRelease(UndeleteReleaseRequest) returns (Release) {
    option (google.api.http) = {post: "/v1/{name=projects/*/releases/*}:undelete"};
    option (bytebase.v1.permission) = "bb.releases.undelete";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc CheckRelease(CheckReleaseRequest) returns (CheckReleaseResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/releases:check"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.releases.check";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message GetReleaseRequest {
  // Format: projects/{project}/releases/{release}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Release"}
  ];
}

message ListReleasesRequest {
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The maximum number of releases to return. The service may return fewer than this value.
  // If unspecified, at most 10 releases will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // Not used.
  // A page token, received from a previous `ListReleases` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReleases` must match
  // the call that provided the page token.
  string page_token = 3;

  // Show deleted releases if specified.
  bool show_deleted = 4;
}

message ListReleasesResponse {
  repeated Release releases = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateReleaseRequest {
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The release to create.
  Release release = 2 [(google.api.field_behavior) = REQUIRED];
}

message UpdateReleaseRequest {
  // The release to update.
  Release release = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

message DeleteReleaseRequest {
  // The name of the release to delete.
  // Format: projects/{project}/releases/{release}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Release"}
  ];
}

message UndeleteReleaseRequest {
  // The name of the deleted release.
  // Format: projects/{project}/releases/{release}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Release"}
  ];
}

message CheckReleaseRequest {
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The release to check.
  Release release = 2 [(google.api.field_behavior) = REQUIRED];

  // The targets to dry-run the release.
  // Can be database or databaseGroup.
  // Format:
  // projects/{project}/databaseGroups/{databaseGroup}
  // instances/{instance}/databases/{database}
  repeated string targets = 3;
}

message CheckReleaseResponse {
  message CheckResult {
    // The file path that is being checked.
    string file = 1;

    // The target that the check is performed on.
    // Should be a database. Format: instances/{instance}/databases/{database}
    string target = 2;

    // The list of advice for the file and the target.
    repeated Advice advices = 3;

    // The count of affected rows of the statement on the target.
    int32 affected_rows = 4;

    // The risk level of the statement on the target.
    RiskLevel risk_level = 5;
  }

  repeated CheckResult results = 1;

  // The affected rows of the check.
  int32 affected_rows = 2;

  enum RiskLevel {
    RISK_LEVEL_UNSPECIFIED = 0;
    LOW = 1;
    MODERATE = 2;
    HIGH = 3;
  }
  // The aggregated risk level of the check.
  RiskLevel risk_level = 3;
}

message Release {
  option (google.api.resource) = {
    type: "bytebase.com/Release"
    pattern: "projects/{project}/releases/{release}"
  };

  // Format: projects/{project}/releases/{release}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  string title = 2;

  repeated File files = 3;

  VCSSource vcs_source = 4;

  // Format: users/<EMAIL>
  string creator = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp create_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  message File {
    // The unique identifier for the file.
    string id = 1;
    // The path of the file. e.g. `2.2/V0001_create_table.sql`.
    string path = 2;
    // The sheet that holds the content.
    // Format: projects/{project}/sheets/{sheet}
    string sheet = 3 [(google.api.resource_reference) = {type: "bytebase.com/Sheet"}];
    // The SHA256 hash value of the sheet.
    string sheet_sha256 = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
    ReleaseFileType type = 5;
    string version = 6;
    ChangeType change_type = 9;

    // The statement is used for preview or check purpose.
    bytes statement = 7;
    int64 statement_size = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    enum ChangeType {
      CHANGE_TYPE_UNSPECIFIED = 0;
      DDL = 1;
      DDL_GHOST = 2;
      DML = 3;
    }
  }

  message VCSSource {
    VCSType vcs_type = 1;
    // The url link to the e.g. GitHub commit or pull request.
    string url = 2;
  }
}

enum ReleaseFileType {
  TYPE_UNSPECIFIED = 0;
  VERSIONED = 1;
}
