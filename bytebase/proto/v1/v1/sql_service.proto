syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/database_catalog_service.proto";
import "v1/database_service.proto";

option go_package = "generated-go/v1";

service SQLService {
  rpc Query(QueryRequest) returns (QueryResponse) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*/databases/*}:query"
      body: "*"

      additional_bindings: {
        post: "/v1/{name=instances/*}:query"
        body: "*"
      }
    };
    option (bytebase.v1.permission) = "bb.databases.get";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc AdminExecute(stream AdminExecuteRequest) returns (stream AdminExecuteResponse) {
    // GRPC streaming / websocket requires GET method instead of POST.
    option (google.api.http) = {get: "/v1:adminExecute"};
    option (bytebase.v1.permission) = "bb.sql.admin";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  // SearchQueryHistories searches query histories for the caller.
  rpc SearchQueryHistories(SearchQueryHistoriesRequest) returns (SearchQueryHistoriesResponse) {
    option (google.api.http) = {
      post: "/v1/queryHistories:search"
      body: "*"
    };
    option (bytebase.v1.auth_method) = CUSTOM;
  }

  rpc Export(ExportRequest) returns (ExportResponse) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*/databases/*}:export"
      body: "*"

      additional_bindings: {
        post: "/v1/{name=instances/*}:export"
        body: "*"
      }
      additional_bindings: {
        post: "/v1/{name=projects/*/issues/*}:export"
        body: "*"
      }
    };
    option (bytebase.v1.permission) = "bb.databases.get";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc Check(CheckRequest) returns (CheckResponse) {
    option (google.api.http) = {
      post: "/v1/sql/check"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.databases.check";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ParseMyBatisMapper(ParseMyBatisMapperRequest) returns (ParseMyBatisMapperResponse) {
    option (google.api.http) = {
      post: "/v1/sql/parseMyBatisMapper"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
  }

  rpc Pretty(PrettyRequest) returns (PrettyResponse) {
    option (google.api.http) = {
      post: "/v1/sql/pretty"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
  }

  rpc DiffMetadata(DiffMetadataRequest) returns (DiffMetadataResponse) {
    option (google.api.http) = {
      post: "/v1/schemaDesign:diffMetadata"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
    // This is a util method requiring no authentication thus no authorization.
  }

  rpc AICompletion(AICompletionRequest) returns (AICompletionResponse) {
    option (google.api.http) = {
      post: "/v1/sql/aiCompletion"
      body: "*"
    };
    option (bytebase.v1.auth_method) = CUSTOM;
  }
}

message AdminExecuteRequest {
  reserved 2;

  // The name is the instance name to execute the query against.
  // Format: instances/{instance}/databases/{databaseName}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Database"}
  ];

  // The SQL statement to execute.
  string statement = 3;

  // The maximum number of rows to return.
  int32 limit = 4;

  // The default schema to execute the statement. Equals to the current schema
  // in Oracle and search path in Postgres.
  optional string schema = 6;

  // Container is the container name to execute the query against, used for
  // CosmosDB only.
  optional string container = 7;
}

message AdminExecuteResponse {
  // The query results.
  repeated QueryResult results = 1;
}

message QueryRequest {
  reserved 2;

  // The name is the instance name to execute the query against.
  // Format: instances/{instance}/databases/{databaseName}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Database"}
  ];

  // The SQL statement to execute.
  string statement = 3;

  // The maximum number of rows to return.
  int32 limit = 4;

  // The id of data source.
  // It is used for querying admin data source even if the instance has
  // read-only data sources. Or it can be used to query a specific read-only
  // data source.
  string data_source_id = 6;

  // Explain the statement.
  bool explain = 7;

  // The default schema to search objects. Equals to the current schema in
  // Oracle and search path in Postgres.
  optional string schema = 8;

  QueryOption query_option = 9;

  // Container is the container name to execute the query against, used for
  // CosmosDB only.
  optional string container = 10;
}

message QueryResponse {
  reserved 2;

  // The query results.
  repeated QueryResult results = 1;
}

message QueryOption {
  enum RedisRunCommandsOn {
    // UNSPECIFIED defaults to SINGLE_NODE.
    REDIS_RUN_COMMANDS_ON_UNSPECIFIED = 0;
    SINGLE_NODE = 1;
    ALL_NODES = 2;
  }
  RedisRunCommandsOn redis_run_commands_on = 1;
}

message QueryResult {
  // Column names of the query result.
  repeated string column_names = 1;

  // Column types of the query result.
  // The types come from the Golang SQL driver.
  repeated string column_type_names = 2;

  // Rows of the query result.
  repeated QueryRow rows = 3;

  int64 rows_count = 10;

  // Columns are masked or not.
  repeated bool masked = 4;

  // Columns are sensitive or not.
  repeated bool sensitive = 5;

  // The error message if the query failed.
  string error = 6;

  // The time it takes to execute the query.
  google.protobuf.Duration latency = 7;

  // The query statement for the result.
  string statement = 8;

  oneof detailed_error {
    PostgresError postgres_error = 9;
  }

  // refer https://www.postgresql.org/docs/11/protocol-error-fields.html
  // for field description.
  message PostgresError {
    string severity = 1;
    string code = 2;
    string message = 3;
    string detail = 4;
    string hint = 5;
    int32 position = 6;
    int32 internal_position = 7;
    string internal_query = 8;
    string where = 9;
    string schema_name = 10;
    string table_name = 11;
    string column_name = 12;
    string data_type_name = 13;
    string constraint_name = 14;
    string file = 15;
    int32 line = 16;
    string routine = 17;
  }

  // The query result is allowed to be exported or not.
  bool allow_export = 11;

  message Message {
    enum Level {
      // Unspecified.
      LEVEL_UNSPECIFIED = 0;
      INFO = 1;
      WARNING = 2;
      DEBUG = 3;
      LOG = 4;
      NOTICE = 5;
      EXCEPTION = 6;
    }

    Level level = 1;
    string content = 2;
  }

  // Informational or debug messages returned by the database engine during query execution.
  // Examples include PostgreSQL's RAISE NOTICE, MSSQL's PRINT, or Oracle's DBMS_OUTPUT.PUT_LINE.
  repeated Message messages = 12;
}

message QueryRow {
  // Row values of the query result.
  repeated RowValue values = 1;
}

message RowValue {
  message Timestamp {
    google.protobuf.Timestamp google_timestamp = 1;
    // The accuracy is the number of digits after the decimal point.
    int32 accuracy = 2;
  }

  message TimestampTZ {
    google.protobuf.Timestamp google_timestamp = 1;
    // Zone is the time zone abbreviations in timezone database such as "PDT",
    // "PST". https://en.wikipedia.org/wiki/List_of_tz_database_time_zones We
    // retrieve the time zone information from the timestamptz field in the
    // database. A timestamp is in UTC or epoch time, and with zone info, we can
    // convert it to a local time string. Zone and offset are returned by
    // time.Time.Zone()
    string zone = 2;
    // The offset is in seconds east of UTC
    int32 offset = 3;
    int32 accuracy = 4;
  }

  oneof kind {
    google.protobuf.NullValue null_value = 1;
    bool bool_value = 2;
    bytes bytes_value = 3;
    double double_value = 4;
    float float_value = 5;
    int32 int32_value = 6;
    int64 int64_value = 7;
    string string_value = 8;
    uint32 uint32_value = 9;
    uint64 uint64_value = 10;
    // value_value is used for Spanner and TUPLE ARRAY MAP in Clickhouse only.
    google.protobuf.Value value_value = 11;
    // timestamp_value is used for the timestamp without time zone data type,
    // meaning it only includes the timestamp without any time zone or location
    // info. Although it may be expressed as a UTC value, it should be seen as a
    // timestamp missing location context.
    Timestamp timestamp_value = 12;
    // timestamp_tz_value is used for the timestamptz data type, which
    // accurately represents the timestamp with location information.
    TimestampTZ timestamp_tz_value = 13;
  }
}

message Advice {
  reserved 7;

  enum Status {
    // Unspecified.
    STATUS_UNSPECIFIED = 0;
    SUCCESS = 1;
    WARNING = 2;
    ERROR = 3;
  }
  // The advice status.
  Status status = 1;

  // The advice code.
  int32 code = 2;

  // The advice title.
  string title = 3;

  // The advice content.
  string content = 4;

  reserved 5, 6;

  // The start_position is inclusive and the end_position is exclusive.
  // TODO: use range instead
  Position start_position = 8;
  Position end_position = 9;
}

message ExportRequest {
  reserved 2;

  // The name is the instance name to execute the query against.
  // Format: instances/{instance}/databases/{databaseName}
  // Format: projects/{project}/issues/{issue} for data export issue.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Database"}
  ];

  // The SQL statement to execute.
  string statement = 3;

  // The maximum number of rows to return.
  int32 limit = 4;

  // The export format.
  ExportFormat format = 5;

  // The admin is used for workspace owner and DBA for exporting data from SQL
  // Editor Admin mode. The exported data is not masked.
  bool admin = 6;

  // The zip password provide by users.
  string password = 7;

  // The id of data source.
  // It is used for querying admin data source even if the instance has
  // read-only data sources. Or it can be used to query a specific read-only
  // data source.
  string data_source_id = 8;
}

message ExportResponse {
  // The export file content.
  bytes content = 1;
}

message PrettyRequest {
  Engine engine = 1;

  // The SDL format SQL schema information that was dumped from a database
  // engine. This information will be sorted to match the order of statements in
  // the userSchema.
  string current_schema = 2;

  // The expected SDL schema. This schema will be checked for correctness and
  // normalized.
  string expected_schema = 3;
}

message PrettyResponse {
  // The pretty-formatted version of current schema.
  string current_schema = 1;

  // The expected SDL schema after normalizing.
  string expected_schema = 2;
}

message CheckRequest {
  // The database name to check against.
  // Format: instances/{instance}/databases/{database}
  string name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Database"}
  ];

  string statement = 1;

  enum ChangeType {
    CHANGE_TYPE_UNSPECIFIED = 0;
    DDL = 1;
    DDL_GHOST = 2;
    DML = 3;
    SQL_EDITOR = 4;
  }
  ChangeType change_type = 4;
}

message CheckResponse {
  repeated Advice advices = 1;

  // The count of affected rows of the statement on the target database.
  int32 affected_rows = 2;
}

message ParseMyBatisMapperRequest {
  bytes content = 1;
}

message ParseMyBatisMapperResponse {
  repeated string statements = 1;
}

message DiffMetadataRequest {
  // The metadata of the source schema.
  DatabaseMetadata source_metadata = 1 [(google.api.field_behavior) = REQUIRED];

  // The metadata of the target schema.
  DatabaseMetadata target_metadata = 2 [(google.api.field_behavior) = REQUIRED];

  DatabaseCatalog source_catalog = 5;

  DatabaseCatalog target_catalog = 6;

  // The database engine of the schema.
  Engine engine = 3;

  // If false, we will build the raw common by classification in database
  // config.
  bool classification_from_config = 4;
}

message DiffMetadataResponse {
  // The diff of the metadata.
  string diff = 1;
}

message SearchQueryHistoriesRequest {
  // The maximum number of histories to return.
  // The service may return fewer than this value.
  // If unspecified, at most 10 history entries will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // A page token, received from a previous `ListQueryHistory` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 2;

  // Filter is the filter to apply on the search query history
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // Supported filter:
  // - project: the project full name in "projects/{id}" format, support "==" operator.
  // - database: the database full name in "instances/{id}/databases/{name}" format, support "==" operator.
  // - instance: the instance full name in "instances/{id}" format, support "==" operator.
  // - type: the type, should be "QUERY" or "EXPORT", support "==" operator.
  // - statement: the SQL statemnt, support ".matches()" operator.
  //
  // For example:
  // project == "projects/{project}"
  // database == "instances/{instance}/databases/{database}"
  // instance == "instances/{instance}"
  // type == "QUERY"
  // type == "EXPORT"
  // statement.matches("select")
  // type == "QUERY" && statement.matches("select")
  string filter = 3;
}

message SearchQueryHistoriesResponse {
  // The list of history.
  repeated QueryHistory query_histories = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A token to retrieve next page of history.
  // Pass this value in the page_token field in the subsequent call to
  // `ListQueryHistory` method to retrieve the next page of history.
  string next_page_token = 2;
}

message QueryHistory {
  // The name for the query history.
  // Format: queryHistories/{uid}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The database name to execute the query.
  // Format: instances/{instance}/databases/{databaseName}
  string database = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  string creator = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  string statement = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  optional string error = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Duration duration = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  enum Type {
    TYPE_UNSPECIFIED = 0;
    QUERY = 1;
    EXPORT = 2;
  }

  Type type = 8;
}

message AICompletionRequest {
  message Message {
    string role = 1;
    string content = 2;
  }
  repeated Message messages = 1;
}

message AICompletionResponse {
  message Candidate {
    message Content {
      message Part {
        string text = 1;
      }
      // parts is used for a result content with multiple parts.
      repeated Part parts = 1;
    }
    Content content = 1;
  }
  // candidates is used for results with multiple choices and candidates. Used
  // for OpenAI and Gemini.
  repeated Candidate candidates = 1;
}
