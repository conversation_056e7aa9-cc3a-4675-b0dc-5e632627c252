syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/type/expr.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service RiskService {
  rpc ListRisks(ListRisksRequest) returns (ListRisksResponse) {
    option (google.api.http) = {get: "/v1/risks"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.risks.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc CreateRisk(CreateRiskRequest) returns (Risk) {
    option (google.api.http) = {
      post: "/v1/risks"
      body: "risk"
    };
    option (google.api.method_signature) = "risk";
    option (bytebase.v1.permission) = "bb.risks.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc GetRisk(GetRiskRequest) returns (Risk) {
    option (google.api.http) = {get: "/v1/{name=risks/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.risks.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc UpdateRisk(UpdateRiskRequest) returns (Risk) {
    option (google.api.http) = {
      patch: "/v1/{risk.name=risks/*}"
      body: "risk"
    };
    option (google.api.method_signature) = "risk,update_mask";
    option (bytebase.v1.permission) = "bb.risks.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc DeleteRisk(DeleteRiskRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=risks/*}"};
    option (bytebase.v1.permission) = "bb.risks.delete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
}

message ListRisksRequest {
  // Not used.
  // The maximum number of risks to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 risks will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // Not used.
  // A page token, received from a previous `ListRisks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `LiskRisks` must match
  // the call that provided the page token.
  string page_token = 2;
}

message ListRisksResponse {
  repeated Risk risks = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateRiskRequest {
  // The risk to create.
  Risk risk = 1 [(google.api.field_behavior) = REQUIRED];
}

message GetRiskRequest {
  // The name of the risk to retrieve.
  // Format: risks/{risk}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Risk"}
  ];
}

message UpdateRiskRequest {
  // The risk to update.
  //
  // The risk's `name` field is used to identify the risk to update.
  // Format: risks/{risk}
  Risk risk = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

message DeleteRiskRequest {
  // The name of the risk to delete.
  // Format: risks/{risk}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Risk"}
  ];
}

message Risk {
  option (google.api.resource) = {
    type: "bytebase.com/Risk"
    pattern: "risks/{risk}"
  };

  reserved 2;

  // Format: risks/{risk}
  string name = 1;

  enum Source {
    SOURCE_UNSPECIFIED = 0;
    DDL = 1;
    DML = 2;
    CREATE_DATABASE = 3;
    DATA_EXPORT = 6;
    REQUEST_ROLE = 7;
  }
  Source source = 3;

  string title = 4;
  int32 level = 5;
  bool active = 7;

  // The condition that is associated with the risk.
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // All supported variables:
  // affected_rows: affected row count in the DDL/DML, support "==", "!=", "<", "<=", ">", ">=" operations.
  // table_rows: table row count number, support "==", "!=", "<", "<=", ">", ">=" operations.
  // environment_id: the environment resource id, support "==", "!=", "in [xx]", "!(in [xx])" operations.
  // project_id: the project resource id, support "==", "!=", "in [xx]", "!(in [xx])", "contains()", "matches()", "startsWith()", "endsWith()" operations.
  // db_engine: the database engine type, support "==", "!=", "in [xx]", "!(in [xx])" operations. Check the Engine enum for the values.
  // sql_type: the SQL type, support "==", "!=", "in [xx]", "!(in [xx])" operations.
  //  when the risk source is DDL, check https://github.com/bytebase/bytebase/blob/main/frontend/src/plugins/cel/types/values.ts#L70 for supported values.
  //  when the risk source is DML, check https://github.com/bytebase/bytebase/blob/main/frontend/src/plugins/cel/types/values.ts#L71 for supported values.
  // database_name: the database name, support "==", "!=", "in [xx]", "!(in [xx])", "contains()", "matches()", "startsWith()", "endsWith()" operations.
  // schema_name: the schema name, support "==", "!=", "in [xx]", "!(in [xx])", "contains()", "matches()", "startsWith()", "endsWith()" operations.
  // table_name: the table name, support "==", "!=", "in [xx]", "!(in [xx])", "contains()", "matches()", "startsWith()", "endsWith()" operations.
  // sql_statement: the SQL statement, support "contains()", "matches()", "startsWith()", "endsWith()" operations.
  // export_rows: export data count, support "==", "!=", "<", "<=", ">", ">=" operations.
  // expiration_days: the role expiration days for the request, support "==", "!=", "<", "<=", ">", ">=" operations.
  // role: the request role full name, support "==", "!=", "in [xx]", "!(in [xx])", "contains()", "matches()", "startsWith()", "endsWith()" operations.
  //
  // When the risk source is DDL/DML, support following variables:
  // affected_rows
  // table_rows
  // environment_id
  // project_id
  // db_engine
  // sql_type
  // database_name
  // schema_name
  // table_name
  // sql_statement
  //
  // When the risk source is CREATE_DATABASE, support following variables:
  // environment_id
  // project_id
  // db_engine
  // database_name
  //
  // When the risk source is DATA_EXPORT, support following variables:
  // environment_id
  // project_id
  // db_engine
  // database_name
  // schema_name
  // table_name
  // export_rows
  //
  // When the risk source is REQUEST_QUERY, support following variables:
  // environment_id
  // project_id
  // db_engine
  // database_name
  // schema_name
  // table_name
  // expiration_days
  //
  // When the risk source is REQUEST_EXPORT, support following variables:
  // environment_id
  // project_id
  // db_engine
  // database_name
  // schema_name
  // table_name
  // expiration_days
  // export_rows
  //
  // When the risk source is REQUEST_ROLE, support following variables:
  // project_id
  // expiration_days
  // role
  google.type.Expr condition = 8;
}
