syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service IdentityProviderService {
  rpc GetIdentityProvider(GetIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {get: "/v1/{name=idps/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.identityProviders.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListIdentityProviders(ListIdentityProvidersRequest) returns (ListIdentityProvidersResponse) {
    option (google.api.http) = {get: "/v1/idps"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.allow_without_credential) = true;
    // The method requires no authentication thus no authorization.
  }

  rpc CreateIdentityProvider(CreateIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {
      post: "/v1/idps"
      body: "identity_provider"
    };
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.identityProviders.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UpdateIdentityProvider(UpdateIdentityProviderRequest) returns (IdentityProvider) {
    option (google.api.http) = {
      patch: "/v1/{identity_provider.name=idps/*}"
      body: "identity_provider"
    };
    option (google.api.method_signature) = "identity_provider,update_mask";
    option (bytebase.v1.permission) = "bb.identityProviders.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc DeleteIdentityProvider(DeleteIdentityProviderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=idps/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.identityProviders.delete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc TestIdentityProvider(TestIdentityProviderRequest) returns (TestIdentityProviderResponse) {
    option (google.api.http) = {
      post: "/v1/idps/*:test"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.identityProviders.update";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message GetIdentityProviderRequest {
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/IdP"}
  ];
}

message ListIdentityProvidersRequest {
  // Not used.
  // The maximum number of identity providers to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // Not used.
  // A page token, received from a previous `ListIdentityProviders` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListIdentityProviders` must match
  // the call that provided the page token.
  string page_token = 2;
}

message ListIdentityProvidersResponse {
  // The identity providers from the specified request.
  repeated IdentityProvider identity_providers = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateIdentityProviderRequest {
  // The identity provider to create.
  IdentityProvider identity_provider = 1 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the identity provider, which will become the final component of
  // the identity provider's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  string identity_provider_id = 2;

  // If set to true, the request will be validated without actually creating the identity provider.
  bool validate_only = 3;
}

message UpdateIdentityProviderRequest {
  // The identity provider to update.
  //
  // The identity provider's `name` field is used to identify the identity provider to update.
  // Format: idps/{identity_provider}
  IdentityProvider identity_provider = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;
}

message DeleteIdentityProviderRequest {
  // The name of the identity provider to delete.
  // Format: idps/{identity_provider}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/IdP"}
  ];
}

message TestIdentityProviderRequest {
  // The identity provider to test connection including uncreated.
  IdentityProvider identity_provider = 1;

  oneof context {
    OAuth2IdentityProviderTestRequestContext oauth2_context = 2;
  }
}

message OAuth2IdentityProviderTestRequestContext {
  // Authorize code from website.
  string code = 1;
}

message TestIdentityProviderResponse {}

message IdentityProvider {
  option (google.api.resource) = {
    type: "bytebase.com/IdP"
    pattern: "idps/{idp}"
  };

  reserved 2;

  // The name of the identity provider.
  // Format: idps/{idp}
  string name = 1;

  string title = 4;

  string domain = 5;

  IdentityProviderType type = 6;

  IdentityProviderConfig config = 7;
}

enum IdentityProviderType {
  IDENTITY_PROVIDER_TYPE_UNSPECIFIED = 0;
  OAUTH2 = 1;
  OIDC = 2;
  LDAP = 3;
}

message IdentityProviderConfig {
  oneof config {
    OAuth2IdentityProviderConfig oauth2_config = 1;
    OIDCIdentityProviderConfig oidc_config = 2;
    LDAPIdentityProviderConfig ldap_config = 3;
  }
}

// OAuth2IdentityProviderConfig is the structure for OAuth2 identity provider config.
message OAuth2IdentityProviderConfig {
  string auth_url = 1;

  string token_url = 2;

  string user_info_url = 3;

  string client_id = 4;

  string client_secret = 5;

  repeated string scopes = 6;

  FieldMapping field_mapping = 7;

  bool skip_tls_verify = 8;

  OAuth2AuthStyle auth_style = 9;
}

// OIDCIdentityProviderConfig is the structure for OIDC identity provider config.
message OIDCIdentityProviderConfig {
  string issuer = 1;

  string client_id = 2;

  string client_secret = 3;

  // The scopes that the OIDC provider supports.
  // Should be fetched from the well-known configuration file of the OIDC provider.
  repeated string scopes = 4;

  FieldMapping field_mapping = 5;

  bool skip_tls_verify = 6;

  OAuth2AuthStyle auth_style = 7;

  // The authorization endpoint of the OIDC provider.
  // Should be fetched from the well-known configuration file of the OIDC provider.
  string auth_endpoint = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// LDAPIdentityProviderConfig is the structure for LDAP identity provider config.
message LDAPIdentityProviderConfig {
  // Host is the hostname or IP address of the LDAP server, e.g.
  // "ldap.example.com".
  string host = 1;
  // Port is the port number of the LDAP server, e.g. 389. When not set, the
  // default port of the corresponding security protocol will be used, i.e. 389
  // for StartTLS and 636 for LDAPS.
  int32 port = 2;
  // SkipTLSVerify controls whether to skip TLS certificate verification.
  bool skip_tls_verify = 3;
  // BindDN is the DN of the user to bind as a service account to perform
  // search requests.
  string bind_dn = 4;
  // BindPassword is the password of the user to bind as a service account.
  string bind_password = 5;
  // BaseDN is the base DN to search for users, e.g. "ou=users,dc=example,dc=com".
  string base_dn = 6;
  // UserFilter is the filter to search for users, e.g. "(uid=%s)".
  string user_filter = 7;
  // SecurityProtocol is the security protocol to be used for establishing
  // connections with the LDAP server. It must be StartTLS, LDAPS or None.
  string security_protocol = 8;
  // FieldMapping is the mapping of the user attributes returned by the LDAP
  // server.
  FieldMapping field_mapping = 9;
}

// FieldMapping saves the field names from user info API of identity provider.
// As we save all raw json string of user info response data into `principal.idp_user_info`,
// we can extract the relevant data based with `FieldMapping`.
message FieldMapping {
  reserved 3;

  // Identifier is the field name of the unique identifier in 3rd-party idp user info. Required.
  string identifier = 1;

  // DisplayName is the field name of display name in 3rd-party idp user info. Optional.
  string display_name = 2;

  // Phone is the field name of primary phone in 3rd-party idp user info. Optional.
  string phone = 4;

  // Groups is the field name of groups in 3rd-party idp user info. Optional.
  // Mainly used for OIDC: https://developer.okta.com/docs/guides/customize-tokens-groups-claim/main/
  string groups = 5;
}

enum OAuth2AuthStyle {
  OAUTH2_AUTH_STYLE_UNSPECIFIED = 0;
  // IN_PARAMS sends the "client_id" and "client_secret" in the POST body
  // as application/x-www-form-urlencoded parameters.
  IN_PARAMS = 1;
  // IN_HEADER sends the client_id and client_password using HTTP Basic Authorization.
  // This is an optional style described in the OAuth2 RFC 6749 section 2.3.1.
  IN_HEADER = 2;
}
