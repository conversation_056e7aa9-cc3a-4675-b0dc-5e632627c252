syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/expr/v1alpha1/syntax.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service CelService {
  rpc BatchParse(BatchParseRequest) returns (BatchParseResponse) {
    option (google.api.http) = {
      post: "/v1/cel/batchParse"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
  }
  rpc BatchDeparse(BatchDeparseRequest) returns (BatchDeparseResponse) {
    option (google.api.http) = {
      post: "/v1/cel/batchDeparse"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
  }
}

message BatchParseRequest {
  repeated string expressions = 1;
}

message BatchParseResponse {
  repeated google.api.expr.v1alpha1.Expr expressions = 1;
}

message BatchDeparseRequest {
  repeated google.api.expr.v1alpha1.Expr expressions = 1;
}

message BatchDeparseResponse {
  repeated string expressions = 1;
}
