syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/expr.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/database_catalog_service.proto";
import "v1/database_service.proto";
import "v1/issue_service.proto";
import "v1/subscription_service.proto";

option go_package = "generated-go/v1";

service SettingService {
  rpc ListSettings(ListSettingsRequest) returns (ListSettingsResponse) {
    option (google.api.http) = {get: "/v1/settings"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.settings.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc GetSetting(GetSettingRequest) returns (Setting) {
    option (google.api.http) = {get: "/v1/{name=settings/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.settings.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc UpdateSetting(UpdateSettingRequest) returns (Setting) {
    option (google.api.http) = {
      patch: "/v1/{setting.name=settings/*}"
      body: "setting"
    };
    option (bytebase.v1.permission) = "bb.settings.set";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
}

message ListSettingsRequest {
  // Not used.
  // The maximum number of settings to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 settings will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // Not used.
  // A page token, received from a previous `ListSettings` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSettings` must match
  // the call that provided the page token.
  string page_token = 2;
}

message ListSettingsResponse {
  // The settings from the specified request.
  repeated Setting settings = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request message for getting a setting.
message GetSettingRequest {
  // The resource name of the setting.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Setting"}
  ];
}

// The response message for getting a setting.
message GetSettingResponse {
  Setting setting = 1;
}

// The request message for updating or creating a setting.
message UpdateSettingRequest {
  // The setting to update.
  Setting setting = 1 [(google.api.field_behavior) = REQUIRED];

  // validate_only is a flag to indicate whether to validate the setting value,
  // server would not persist the setting value if it is true.
  bool validate_only = 2;

  bool allow_missing = 3;

  google.protobuf.FieldMask update_mask = 4;
}

// The schema of setting.
message Setting {
  option (google.api.resource) = {
    type: "bytebase.com/Setting"
    pattern: "settings/{setting}"
  };

  // The resource name of the setting. Must be one of the following forms:
  //
  // - `setting/{setting}`
  // For example, "settings/bb.branding.logo"
  string name = 1;

  // The value of the setting.
  Value value = 2;
}

// The data in setting value.
message Value {
  // Value is a oneof field for setting value.
  oneof value {
    // Defines this value as being a string value.
    string string_value = 1;
    SMTPMailDeliverySettingValue smtp_mail_delivery_setting_value = 2;
    AppIMSetting app_im_setting_value = 3;
    AgentPluginSetting agent_plugin_setting_value = 4;
    WorkspaceProfileSetting workspace_profile_setting_value = 5;
    WorkspaceApprovalSetting workspace_approval_setting_value = 6;
    WorkspaceTrialSetting workspace_trial_setting_value = 7;
    SchemaTemplateSetting schema_template_setting_value = 9;
    DataClassificationSetting data_classification_setting_value = 10;
    SemanticTypeSetting semantic_type_setting_value = 11;
    MaximumSQLResultSizeSetting maximum_sql_result_size_setting = 13;
    SCIMSetting scim_setting = 14;
    PasswordRestrictionSetting password_restriction_setting = 15;
    AISetting ai_setting = 16;
    EnvironmentSetting environment_setting = 17;
  }
}

message SMTPMailDeliverySettingValue {
  // The SMTP server address.
  string server = 1;

  // The SMTP server port.
  int32 port = 2;

  // We support three types of SMTP encryption: NONE, STARTTLS, and SSL/TLS.
  enum Encryption {
    ENCRYPTION_UNSPECIFIED = 0;
    ENCRYPTION_NONE = 1;
    ENCRYPTION_STARTTLS = 2;
    ENCRYPTION_SSL_TLS = 3;
  }

  // The SMTP server encryption.
  Encryption encryption = 3;

  // The CA, KEY, and CERT for the SMTP server.
  // Not used.
  optional string ca = 4;
  optional string key = 5;
  optional string cert = 6;

  // We support four types of SMTP authentication: NONE, PLAIN, LOGIN, and CRAM-MD5.
  enum Authentication {
    AUTHENTICATION_UNSPECIFIED = 0;
    AUTHENTICATION_NONE = 1;
    AUTHENTICATION_PLAIN = 2;
    AUTHENTICATION_LOGIN = 3;
    AUTHENTICATION_CRAM_MD5 = 4;
  }
  Authentication authentication = 7;
  string username = 8;
  // If not specified, server will use the existed password.
  optional string password = 9;

  // The sender email address.
  string from = 10;

  // The recipient email address, used with validate_only to send test email.
  string to = 11;
}

message AppIMSetting {
  message Slack {
    bool enabled = 1;
    string token = 2 [(google.api.field_behavior) = INPUT_ONLY];
  }
  message Feishu {
    bool enabled = 1;
    string app_id = 2 [(google.api.field_behavior) = INPUT_ONLY];
    string app_secret = 3 [(google.api.field_behavior) = INPUT_ONLY];
  }
  message Wecom {
    bool enabled = 1;
    string corp_id = 2 [(google.api.field_behavior) = INPUT_ONLY];
    string agent_id = 3 [(google.api.field_behavior) = INPUT_ONLY];
    string secret = 4 [(google.api.field_behavior) = INPUT_ONLY];
  }
  message Lark {
    bool enabled = 1;
    string app_id = 2 [(google.api.field_behavior) = INPUT_ONLY];
    string app_secret = 3 [(google.api.field_behavior) = INPUT_ONLY];
  }
  message DingTalk {
    bool enabled = 1;
    string client_id = 2 [(google.api.field_behavior) = INPUT_ONLY];
    string client_secret = 3 [(google.api.field_behavior) = INPUT_ONLY];
    string robot_code = 4 [(google.api.field_behavior) = INPUT_ONLY];
  }

  Slack slack = 1;
  Feishu feishu = 2;
  Wecom wecom = 3;
  Lark lark = 4;
  DingTalk dingtalk = 5;
}

message AgentPluginSetting {
  // The URL for the agent API.
  string url = 1;

  // The token for the agent.
  string token = 2;
}

message WorkspaceProfileSetting {
  // The external URL is used for sso authentication callback.
  string external_url = 1;

  // Disallow self-service signup, users can only be invited by the owner.
  bool disallow_signup = 2;

  // Require 2FA for all users.
  bool require_2fa = 3;

  // outbound_ip_list is the outbound IP for Bytebase instance in SaaS mode.
  repeated string outbound_ip_list = 4;

  // The duration for token.
  google.protobuf.Duration token_duration = 6;

  // The setting of custom announcement
  Announcement announcement = 7;

  // The max duration for role expired.
  google.protobuf.Duration maximum_role_expiration = 8;

  // The workspace domain, e.g. bytebase.com.
  repeated string domains = 9;

  // Only user and group from the domains can be created and login.
  bool enforce_identity_domain = 10;

  // The workspace database change mode.
  DatabaseChangeMode database_change_mode = 11;

  // Whether to disallow password signin. (Except workspace admins)
  bool disallow_password_signin = 12;
}

message Announcement {
  // We support three levels of AlertLevel: INFO, WARNING, and ERROR.
  enum AlertLevel {
    ALERT_LEVEL_UNSPECIFIED = 0;
    ALERT_LEVEL_INFO = 1;
    ALERT_LEVEL_WARNING = 2;
    ALERT_LEVEL_CRITICAL = 3;
  }

  // The alert level of announcemnt
  AlertLevel level = 1;

  // The text of announcemnt
  string text = 2;

  // The optional link, user can follow the link to check extra details
  string link = 3;
}

message WorkspaceApprovalSetting {
  message Rule {
    ApprovalTemplate template = 2;
    // The condition that is associated with the rule.
    // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
    //
    // Support variables:
    // source: the risk source, check the Source enum in the Risk message for the values, support "==" operator.
    // level: the risk level, support 100 (low), 200 (moderate) and 300 (high), support "==" operator.
    //
    // For examples:
    // (source == "DML" && level == 200) || (source == "DDL" && level == 300)
    google.type.Expr condition = 3;
  }
  repeated Rule rules = 1;
}

message SchemaTemplateSetting {
  message FieldTemplate {
    string id = 1;

    Engine engine = 2;

    string category = 3;

    ColumnMetadata column = 4;

    ColumnCatalog catalog = 5;
  }

  repeated FieldTemplate field_templates = 1;

  message ColumnType {
    Engine engine = 1;

    bool enabled = 2;

    repeated string types = 3;
  }

  repeated ColumnType column_types = 2;

  message TableTemplate {
    string id = 1;

    Engine engine = 2;

    string category = 3;

    TableMetadata table = 4;

    TableCatalog catalog = 5;
  }

  repeated TableTemplate table_templates = 3;
}

message WorkspaceTrialSetting {
  int32 instance_count = 1;

  google.protobuf.Timestamp expire_time = 2;

  google.protobuf.Timestamp issued_time = 3;

  string subject = 4;

  string org_name = 5;

  PlanType plan = 6;
}

message DataClassificationSetting {
  message DataClassificationConfig {
    // id is the uuid for classification. Each project can chose one classification config.
    string id = 1;
    string title = 2;

    message Level {
      string id = 1;
      string title = 2;
      string description = 3;
    }

    // levels is user defined level list for classification.
    // The order for the level decides its priority.
    repeated Level levels = 3;

    message DataClassification {
      // id is the classification id in [0-9]+-[0-9]+-[0-9]+ format.
      string id = 1;
      string title = 2;
      string description = 3;
      optional string level_id = 4;
    }

    // classification is the id - DataClassification map.
    // The id should in [0-9]+-[0-9]+-[0-9]+ format.
    map<string, DataClassification> classification = 4;

    // If true, we will only store the classification in the config.
    // Otherwise we will get the classification from table/column comment,
    // and write back to the schema metadata.
    bool classification_from_config = 5;
  }

  repeated DataClassificationConfig configs = 1;
}

message SemanticTypeSetting {
  message SemanticType {
    // id is the uuid for semantic type.
    string id = 1;
    // the title of the semantic type, it should not be empty.
    string title = 2;
    // the description of the semantic type, it can be empty.
    string description = 3;

    Algorithm algorithm = 6;
  }

  repeated SemanticType types = 1;
}

message Algorithm {
  message FullMask {
    // substitution is the string used to replace the original value, the
    // max length of the string is 16 bytes.
    string substitution = 1;
  }

  message RangeMask {
    message Slice {
      // start is the start index of the original value, start from 0 and should be less than stop.
      int32 start = 1;
      // stop is the stop index of the original value, should be less than the length of the original value.
      int32 end = 2;
      // substitution is the string used to replace the OriginalValue[start:end).
      string substitution = 3;
    }
    // We store it as a repeated field to face the fact that the original value may have multiple parts should be masked.
    // But frontend can be started with a single rule easily.
    repeated Slice slices = 1;
  }

  message MD5Mask {
    // salt is the salt value to generate a different hash that with the word alone.
    string salt = 1;
  }

  message InnerOuterMask {
    int32 prefix_len = 1;
    int32 suffix_len = 2;

    enum MaskType {
      MASK_TYPE_UNSPECIFIED = 0;
      INNER = 1;
      OUTER = 2;
    }

    MaskType type = 3;

    string substitution = 4;
  }

  oneof mask {
    FullMask full_mask = 5;
    RangeMask range_mask = 6;
    MD5Mask md5_mask = 7;
    InnerOuterMask inner_outer_mask = 8;
  }
}

enum DatabaseChangeMode {
  DATABASE_CHANGE_MODE_UNSPECIFIED = 0;
  // A more advanced database change process, including custom approval workflows and other advanced features.
  // Default to this mode.
  PIPELINE = 1;
  // A simple database change process in SQL editor. Users can execute SQL directly.
  EDITOR = 2;
}

message MaximumSQLResultSizeSetting {
  // The limit is in bytes.
  // The default value is 100MB, we will use the default value if the setting not exists, or the limit <= 0.
  int64 limit = 1;
}

message SCIMSetting {
  string token = 1;
}

message PasswordRestrictionSetting {
  // min_length is the minimum length for password, should no less than 8.
  int32 min_length = 1;
  // require_number requires the password must contains at least one number.
  bool require_number = 2;
  // require_letter requires the password must contains at least one letter, regardless of upper case or lower case
  bool require_letter = 3;
  // require_uppercase_letter requires the password must contains at least one upper case letter.
  bool require_uppercase_letter = 4;
  // require_uppercase_letter requires the password must contains at least one special character.
  bool require_special_character = 5;
  // require_reset_password_for_first_login requires users to reset their password after the 1st login.
  bool require_reset_password_for_first_login = 6;
  // password_rotation requires users to reset their password after the duration.
  google.protobuf.Duration password_rotation = 7;
}

message AISetting {
  bool enabled = 1;
  enum Provider {
    PROVIDER_UNSPECIFIED = 0;
    OPEN_AI = 1;
    CLAUDE = 2;
    GEMINI = 3;
    AZURE_OPENAI = 4;
  }
  Provider provider = 2;
  string endpoint = 3;
  string api_key = 4;
  string model = 5;
  string version = 6;
}

message EnvironmentSetting {
  repeated Environment environments = 1;

  message Environment {
    // The resource name of the environment.
    // Format: environments/{environment}.
    // Output only.
    string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
    // The resource id of the environment.
    // This value should be 4-63 characters, and valid characters
    // are /[a-z][0-9]-/.
    string id = 2;
    // The display name of the environment.
    string title = 3;
    map<string, string> tags = 4;
    string color = 5;
  }
}
