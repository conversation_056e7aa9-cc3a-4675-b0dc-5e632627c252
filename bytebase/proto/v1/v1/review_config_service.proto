syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "v1/annotation.proto";
import "v1/org_policy_service.proto";

option go_package = "generated-go/v1";

service ReviewConfigService {
  rpc CreateReviewConfig(CreateReviewConfigRequest) returns (ReviewConfig) {
    option (google.api.http) = {
      post: "/v1/reviewConfigs"
      body: "review_config"
    };
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.reviewConfigs.create";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListReviewConfigs(ListReviewConfigsRequest) returns (ListReviewConfigsResponse) {
    option (google.api.http) = {get: "/v1/reviewConfigs"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.reviewConfigs.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc GetReviewConfig(GetReviewConfigRequest) returns (ReviewConfig) {
    option (google.api.http) = {get: "/v1/{name=reviewConfigs/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.reviewConfigs.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc UpdateReviewConfig(UpdateReviewConfigRequest) returns (ReviewConfig) {
    option (google.api.http) = {
      patch: "/v1/{review_config.name=reviewConfigs/*}"
      body: "review_config"
    };
    option (google.api.method_signature) = "review_config,update_mask";
    option (bytebase.v1.permission) = "bb.reviewConfigs.update";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc DeleteReviewConfig(DeleteReviewConfigRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=reviewConfigs/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.reviewConfigs.delete";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message ListReviewConfigsRequest {
  // Not used.
  // The maximum number of sql review to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 sql review will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // Not used.
  // A page token, provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReviewConfigs` must match
  // the call that provided the page token.
  string page_token = 2;
}

message ListReviewConfigsResponse {
  // The sql review from the specified request.
  repeated ReviewConfig review_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateReviewConfigRequest {
  // The sql review to create.
  ReviewConfig review_config = 1 [(google.api.field_behavior) = REQUIRED];
}

message UpdateReviewConfigRequest {
  // The sql review to update.
  //
  // The name field is used to identify the sql review to update.
  ReviewConfig review_config = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the config is not found, a new config will be created.
  // In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

message GetReviewConfigRequest {
  // The name of the sql review to retrieve.
  // Format: reviewConfigs/{reviewConfig}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/ReviewConfig"}
  ];
}

message DeleteReviewConfigRequest {
  // The name of the sql review to delete.
  // Format: reviewConfigs/{reviewConfig}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/ReviewConfig"}
  ];
}

message ReviewConfig {
  option (google.api.resource) = {
    type: "bytebase.com/ReviewConfig"
    pattern: "reviewConfigs/{reviewConfig}"
  };

  // The name of the sql review to retrieve.
  // Format: reviewConfigs/{reviewConfig}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  string title = 2;

  bool enabled = 3;

  repeated SQLReviewRule rules = 7;

  // resources using the config.
  // Format: {resurce}/{resource id}, for example, environments/test.
  repeated string resources = 8;
}
