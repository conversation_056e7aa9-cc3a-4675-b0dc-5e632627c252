syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "v1/annotation.proto";
import "v1/user_service.proto";

option go_package = "generated-go/v1";

service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/v1/auth/login"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
    option (bytebase.v1.audit) = true;
  }

  rpc Logout(LogoutRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/auth/logout"
      body: "*"
    };
    option (bytebase.v1.allow_without_credential) = true;
    option (bytebase.v1.audit) = true;
  }
}

message LoginRequest {
  string email = 1;

  string password = 2;

  // If web is set, we will set access token, refresh token, and user to the cookie.
  bool web = 3;

  // The name of the identity provider.
  // Format: idps/{idp}
  string idp_name = 4;

  // The idp_context is using to get the user information from identity provider.
  IdentityProviderContext idp_context = 5;

  // The otp_code is used to verify the user's identity by MFA.
  optional string otp_code = 6;

  // The recovery_code is used to recovery the user's identity with MFA.
  optional string recovery_code = 7;

  // The mfa_temp_token is used to verify the user's identity by MFA.
  optional string mfa_temp_token = 8;
}

message IdentityProviderContext {
  oneof context {
    OAuth2IdentityProviderContext oauth2_context = 1;
    OIDCIdentityProviderContext oidc_context = 2;
  }
}

message OAuth2IdentityProviderContext {
  string code = 1;
}

message OIDCIdentityProviderContext {}

message LoginResponse {
  string token = 1;

  optional string mfa_temp_token = 2;

  bool require_reset_password = 3;

  // The user of successful login.
  User user = 4;
}

message LogoutRequest {}
