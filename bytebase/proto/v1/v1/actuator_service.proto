syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/setting_service.proto";
import "v1/user_service.proto";

option go_package = "generated-go/v1";

service ActuatorService {
  rpc GetActuatorInfo(GetActuatorInfoRequest) returns (ActuatorInfo) {
    option (google.api.http) = {get: "/v1/actuator/info"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.allow_without_credential) = true;
  }

  rpc UpdateActuatorInfo(UpdateActuatorInfoRequest) returns (ActuatorInfo) {
    option (google.api.http) = {
      patch: "/v1/actuator/info"
      body: "actuator"
    };
    option (google.api.method_signature) = "actuator,update_mask";
    option (bytebase.v1.permission) = "bb.settings.set";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc SetupSample(SetupSampleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/actuator:setupSample"
      body: ""
    };
    option (bytebase.v1.permission) = "bb.projects.create";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc DeleteCache(DeleteCacheRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/actuator/cache"};
    option (bytebase.v1.allow_without_credential) = true;
  }

  rpc GetResourcePackage(GetResourcePackageRequest) returns (ResourcePackage) {
    option (google.api.http) = {get: "/v1/actuator/resources"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.allow_without_credential) = true;
  }
}

// The request message for getting the theme resource.
message GetResourcePackageRequest {}

// The theme resources.
message ResourcePackage {
  // The branding logo.
  bytes logo = 1;
}

message SetupSampleRequest {}

message GetActuatorInfoRequest {}

message UpdateActuatorInfoRequest {
  // The actuator to update.
  ActuatorInfo actuator = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

message DeleteCacheRequest {}

// ServerInfo is the API message for server info.
// Actuator concept is similar to the Spring Boot Actuator.
message ActuatorInfo {
  // version is the bytebase's server version
  string version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // git_commit is the git commit hash of the build
  string git_commit = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // readonly flag means if the Bytebase is running in readonly mode.
  bool readonly = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // saas flag means if the Bytebase is running in SaaS mode, some features are not allowed to edit by users.
  bool saas = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // demo flag means if the Bytebase is running in demo mode.
  bool demo = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // host is the Bytebase instance host.
  string host = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // port is the Bytebase instance port.
  string port = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // external_url is the URL where user or webhook callback visits Bytebase.
  string external_url = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // need_admin_setup flag means the Bytebase instance doesn't have any end users.
  bool need_admin_setup = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // disallow_signup is the flag to disable self-service signup.
  bool disallow_signup = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // last_active_time is the service last active time in UTC Time Format, any API calls will refresh this value.
  google.protobuf.Timestamp last_active_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // require_2fa is the flag to require 2FA for all users.
  bool require_2fa = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // workspace_id is the identifier for the workspace.
  string workspace_id = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // debug flag means if the debug mode is enabled.
  bool debug = 15;

  repeated string unlicensed_features = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // disallow_password_signin is the flag to disallow user signin with email&password. (except workspace admins)
  bool disallow_password_signin = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  PasswordRestrictionSetting password_restriction = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // docker flag means if the Bytebase instance is running in docker.
  bool docker = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  message StatUser {
    UserType user_type = 1;
    State state = 2;
    int32 count = 3;
  }

  repeated StatUser user_stats = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  int32 activated_instance_count = 24 [(google.api.field_behavior) = OUTPUT_ONLY];
  int32 total_instance_count = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  bool enable_sample = 26 [(google.api.field_behavior) = OUTPUT_ONLY];
}
