syntax = "proto3";

package bytebase.v1;

import "google/api/expr/v1alpha1/syntax.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/type/expr.proto";

option go_package = "generated-go/v1";

message GetIamPolicyRequest {
  // The name of the resource to get the IAM policy.
  // Format: projects/{project}
  // Format: workspaces/{workspace}
  string resource = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/IAMPolicy"}
  ];
}

message SetIamPolicyRequest {
  // The name of the resource to set the IAM policy.
  // Format: projects/{project}
  // Format: workspaces/{workspace}
  string resource = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/IAMPolicy"}
  ];

  IamPolicy policy = 2 [(google.api.field_behavior) = REQUIRED];

  // The current etag of the policy.
  string etag = 3;
}

message IamPolicy {
  // Collection of binding.
  // A binding binds one or more project members to a single project role.
  repeated Binding bindings = 1;

  // The current etag of the policy.
  // If an etag is provided and does not match the current etag of the poliy,
  // the call will be blocked and an ABORTED error will be returned.
  string etag = 2;
}

message Binding {
  // The role that is assigned to the members.
  // Format: roles/{role}
  string role = 1;

  // Specifies the principals requesting access for a Bytebase resource.
  // For users, the member should be: user:{email}
  // For groups, the member should be: group:{email}
  repeated string members = 2;

  // The condition that is associated with this binding, only used in the project IAM policy.
  // If the condition evaluates to true, then this binding applies to the current request.
  // If the condition evaluates to false, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding.
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // Support variables:
  // resource.database: the database full name in "instances/{instance}/databases/{database}" format, used by the "roles/sqlEditorUser" and "roles/projectExporter" role, support "==" operator.
  // resource.schema: the schema name, used by the "roles/sqlEditorUser" and "roles/projectExporter" role, support "==" operator.
  // resource.table: the table name, used by the "roles/sqlEditorUser" and "roles/projectExporter" role, support "==" operator.
  // request.time: the expiration. Only support "<" operation in `request.time < timestamp("{ISO datetime string format}")`.
  // request.row_limit: the maximum export rows, used by the "roles/projectExporter" role. Only support "<=" operation.
  //
  // For example:
  // resource.database == "instances/local-pg/databases/postgres" && resource.schema in ["public","another_schema"]
  // resource.database == "instances/local-pg/databases/bytebase" && resource.schema == "public" && resource.table in ["audit_log"]
  // request.time < timestamp("2025-04-26T11:24:48.655Z") && request.row_limit <= 1000
  google.type.Expr condition = 3;

  // The parsed expression of the condition.
  google.api.expr.v1alpha1.Expr parsed_expr = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The difference delta between two policies.
message PolicyDelta {
  // The delta for Bindings between two policies.
  repeated BindingDelta binding_deltas = 1;
}

// One delta entry for Binding. Each individual change (only one member in each
// entry) to a binding will be a separate entry.
message BindingDelta {
  // The type of action performed on a Binding in a policy.
  enum Action {
    ACTION_UNSPECIFIED = 0;
    ADD = 1;
    REMOVE = 2;
  }

  // The action that was performed on a Binding.
  Action action = 1;

  // Role that is assigned to `members`.
  // For example, `roles/projectOwner`.
  string role = 2;

  // Follows the same format of Binding.members.
  string member = 3;

  // The condition that is associated with this binding.
  google.type.Expr condition = 4;
}
