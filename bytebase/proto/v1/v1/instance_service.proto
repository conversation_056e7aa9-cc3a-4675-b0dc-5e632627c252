syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/instance_role_service.proto";

option go_package = "generated-go/v1";

service InstanceService {
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {get: "/v1/{name=instances/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.instances.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {get: "/v1/instances"};
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.instances.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc CreateInstance(CreateInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      post: "/v1/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "instance";
    option (bytebase.v1.permission) = "bb.instances.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UpdateInstance(UpdateInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (bytebase.v1.permission) = "bb.instances.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc DeleteInstance(DeleteInstanceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=instances/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.instances.delete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UndeleteInstance(UndeleteInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*}:undelete"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.undelete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc SyncInstance(SyncInstanceRequest) returns (SyncInstanceResponse) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*}:sync"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.sync";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListInstanceDatabase(ListInstanceDatabaseRequest) returns (ListInstanceDatabaseResponse) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*}:databases"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc BatchSyncInstances(BatchSyncInstancesRequest) returns (BatchSyncInstancesResponse) {
    option (google.api.http) = {
      post: "/v1/instances:batchSync"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.sync";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc AddDataSource(AddDataSourceRequest) returns (Instance) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*}:addDataSource"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc RemoveDataSource(RemoveDataSourceRequest) returns (Instance) {
    option (google.api.http) = {
      post: "/v1/{name=instances/*}:removeDataSource"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UpdateDataSource(UpdateDataSourceRequest) returns (Instance) {
    option (google.api.http) = {
      patch: "/v1/{name=instances/*}:updateDataSource"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.instances.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
}

message GetInstanceRequest {
  // The name of the instance to retrieve.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];
}

message ListInstancesRequest {
  // The maximum number of instances to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 instances will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // A page token, received from a previous `ListInstances` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListInstances` must match
  // the call that provided the page token.
  string page_token = 2;

  // Show deleted instances if specified.
  bool show_deleted = 3;

  // Filter the instance.
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // Supported filters:
  // - name: the instance name, support "==" and ".matches()" operator.
  // - resource_id: the instance id, support "==" and ".matches()" operator.
  // - environment: the environment full name in "environments/{id}" format, support "==" operator.
  // - state: the instance state, check State enum for values, support "==" operator.
  // - engine: the instance engine, check Engine enum for values. Support "==", "in [xx]", "!(in [xx])" operator.
  // - host: the instance host, support "==" and ".matches()" operator.
  // - port: the instance port, support "==" and ".matches()" operator.
  // - project: the project full name in "projects/{id}" format, support "==" operator.
  //
  // For example:
  // name == "sample instance"
  // name.matches("sample")
  // resource_id = "sample-instance"
  // resource_id.matches("sample")
  // state == "DELETED"
  // environment == "environments/test"
  // engine == "MYSQL"
  // engine in ["MYSQL", "POSTGRES"]
  // !(engine in ["MYSQL", "POSTGRES"])
  // host == "127.0.0.1"
  // host.matches("127.0")
  // port == "54321"
  // port.matches("543")
  // project == "projects/sample-project"
  // You can combine filter conditions like:
  // name.matches("sample") && environment == "environments/test"
  // host == "127.0.0.1" && port == "54321"
  string filter = 4;
}

message ListInstancesResponse {
  // The instances from the specified request.
  repeated Instance instances = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateInstanceRequest {
  // The instance to create.
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the instance, which will become the final component of
  // the instance's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  string instance_id = 2;

  // Validate only also tests the data source connection.
  bool validate_only = 3;
}

message UpdateInstanceRequest {
  // The instance to update.
  //
  // The instance's `name` field is used to identify the instance to update.
  // Format: instances/{instance}
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;
}

message DeleteInstanceRequest {
  // The name of the instance to delete.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // If set to true, any databases and sheets from this project will also be moved to default project, and all open issues will be closed.
  bool force = 2;
}

message UndeleteInstanceRequest {
  // The name of the deleted instance.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];
}

message SyncInstanceRequest {
  // The name of instance.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // When full sync is enabled, all databases in the instance will be synchronized. Otherwise, only
  // the instance metadata (such as the database list) and any newly discovered instances will be synced.
  bool enable_full_sync = 2;
}

message ListInstanceDatabaseRequest {
  // The name of the instance.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // The target instance. We need to set this field if the target instance is not created yet.
  optional Instance instance = 2 [(google.api.field_behavior) = REQUIRED];
}

message ListInstanceDatabaseResponse {
  // All database name list in the instance.
  repeated string databases = 1;
}

message SyncInstanceResponse {
  // All database name list in the instance.
  repeated string databases = 1;
}

message BatchSyncInstancesRequest {
  // The request message specifying the instances to sync.
  // A maximum of 1000 instances can be synced in a batch.
  repeated SyncInstanceRequest requests = 1 [(google.api.field_behavior) = REQUIRED];
}

message BatchSyncInstancesResponse {}

message AddDataSourceRequest {
  // The name of the instance to add a data source to.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // Identified by data source ID.
  // Only READ_ONLY data source can be added.
  DataSource data_source = 2 [(google.api.field_behavior) = REQUIRED];

  // Validate only also tests the data source connection.
  bool validate_only = 3;
}

message RemoveDataSourceRequest {
  // The name of the instance to remove a data source from.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // Identified by data source ID.
  // Only READ_ONLY data source can be removed.
  DataSource data_source = 2 [(google.api.field_behavior) = REQUIRED];
}

message UpdateDataSourceRequest {
  // The name of the instance to update a data source.
  // Format: instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Instance"}
  ];

  // Identified by data source ID.
  DataSource data_source = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 3;

  // Validate only also tests the data source connection.
  bool validate_only = 4;
}

message Instance {
  option (google.api.resource) = {
    type: "bytebase.com/Instance"
    pattern: "instances/{instance}"
  };

  // The name of the instance.
  // Format: instances/{instance}
  string name = 1;

  State state = 3;

  string title = 4;

  Engine engine = 5;

  string engine_version = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  string external_link = 7;

  repeated DataSource data_sources = 8;

  // The environment resource.
  // Format: environments/prod where prod is the environment resource ID.
  string environment = 9 [(google.api.field_behavior) = OPTIONAL];

  bool activation = 10;

  repeated InstanceRole roles = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // How often the instance is synced.
  google.protobuf.Duration sync_interval = 13;

  // The maximum number of connections.
  // The default is 10 if the value is unset or zero.
  int32 maximum_connections = 14;

  // Enable sync for following databases.
  // Default empty, means sync all schemas & databases.
  repeated string sync_databases = 15;
}

message DataSourceExternalSecret {
  enum SecretType {
    SAECRET_TYPE_UNSPECIFIED = 0;
    // ref: https://developer.hashicorp.com/vault/api-docs/secret/kv/kv-v2
    VAULT_KV_V2 = 1;
    // ref: https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html
    AWS_SECRETS_MANAGER = 2;
    // ref: https://cloud.google.com/secret-manager/docs
    GCP_SECRET_MANAGER = 3;
  }
  SecretType secret_type = 1;
  string url = 2;

  enum AuthType {
    AUTH_TYPE_UNSPECIFIED = 0;
    // ref: https://developer.hashicorp.com/vault/docs/auth/token
    TOKEN = 1;
    // ref: https://developer.hashicorp.com/vault/docs/auth/approle
    VAULT_APP_ROLE = 2;
  }
  AuthType auth_type = 3;

  message AppRoleAuthOption {
    string role_id = 1 [(google.api.field_behavior) = INPUT_ONLY];
    // the secret id for the role without ttl.
    string secret_id = 2 [(google.api.field_behavior) = INPUT_ONLY];

    enum SecretType {
      SECRET_TYPE_UNSPECIFIED = 0;
      PLAIN = 1;
      ENVIRONMENT = 2;
    }

    SecretType type = 3;

    // The path where the approle auth method is mounted.
    string mount_path = 4;
  }

  oneof auth_option {
    AppRoleAuthOption app_role = 4;
    string token = 5 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // engine name is the name for secret engine.
  string engine_name = 6;
  // the secret name in the engine to store the password.
  string secret_name = 7;
  // the key name for the password.
  string password_key_name = 8;
}

message DataSource {
  string id = 1;
  DataSourceType type = 2;
  string username = 3;
  string password = 4 [(google.api.field_behavior) = INPUT_ONLY];
  // Use SSL to connect to the data source. By default, we use system default SSL configuration.
  bool use_ssl = 30;
  string ssl_ca = 5 [(google.api.field_behavior) = INPUT_ONLY];
  string ssl_cert = 6 [(google.api.field_behavior) = INPUT_ONLY];
  string ssl_key = 7 [(google.api.field_behavior) = INPUT_ONLY];
  string host = 8;
  string port = 9;
  string database = 10;
  // srv, authentication_database and replica_set are used for MongoDB.
  // srv is a boolean flag that indicates whether the host is a DNS SRV record.
  bool srv = 11;
  // authentication_database is the database name to authenticate against, which stores the user credentials.
  string authentication_database = 12;
  // replica_set is used for MongoDB replica set.
  string replica_set = 25;

  // sid and service_name are used for Oracle.
  string sid = 13;
  string service_name = 14;
  // Connection over SSH.
  // The hostname of the SSH server agent.
  // Required.
  string ssh_host = 15;
  // The port of the SSH server agent. It's 22 typically.
  // Required.
  string ssh_port = 16;
  // The user to login the server.
  // Required.
  string ssh_user = 17;
  // The password to login the server. If it's empty string, no password is required.
  string ssh_password = 18 [(google.api.field_behavior) = INPUT_ONLY];
  // The private key to login the server. If it's empty string, we will use the system default private key from os.Getenv("SSH_AUTH_SOCK").
  string ssh_private_key = 19 [(google.api.field_behavior) = INPUT_ONLY];
  // PKCS#8 private key in PEM format. If it's empty string, no private key is required.
  // Used for authentication when connecting to the data source.
  string authentication_private_key = 20 [(google.api.field_behavior) = INPUT_ONLY];

  DataSourceExternalSecret external_secret = 21;

  enum AuthenticationType {
    AUTHENTICATION_UNSPECIFIED = 0;
    PASSWORD = 1;
    GOOGLE_CLOUD_SQL_IAM = 2;
    AWS_RDS_IAM = 3;
    AZURE_IAM = 4;
  }
  AuthenticationType authentication_type = 22;

  message ClientSecretCredential {
    string tenant_id = 1;
    string client_id = 2;
    string client_secret = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
  oneof iam_extension {
    ClientSecretCredential client_secret_credential = 23;
  }

  SASLConfig sasl_config = 24;

  message Address {
    string host = 1;
    string port = 2;
  }
  // additional_addresses is used for MongoDB replica set.
  repeated Address additional_addresses = 26 [(google.api.field_behavior) = OPTIONAL];

  // direct_connection is used for MongoDB to dispatch all the operations to the node specified in the connection string.
  bool direct_connection = 27;

  // region is the location of where the DB is, works for AWS RDS. For example, us-east-1.
  string region = 28;

  // warehouse_id is used by Databricks.
  string warehouse_id = 29;

  // master_name is the master name used by connecting redis-master via redis sentinel.
  string master_name = 31;

  // master_username and master_password are master credentials used by redis sentinel mode.
  string master_username = 32;
  string master_password = 33;

  enum RedisType {
    REDIS_TYPE_UNSPECIFIED = 0;
    STANDALONE = 1;
    SENTINEL = 2;
    CLUSTER = 3;
  }
  RedisType redis_type = 34;

  // Cluster is the cluster name for the data source. Used by CockroachDB.
  string cluster = 35;

  // Extra connection parameters for the database connection.
  // For PostgreSQL HA, this can be used to set target_session_attrs=read-write
  map<string, string> extra_connection_parameters = 36;
}

enum DataSourceType {
  DATA_SOURCE_UNSPECIFIED = 0;
  ADMIN = 1;
  READ_ONLY = 2;
}

message InstanceResource {
  string title = 1;

  Engine engine = 2;

  string engine_version = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  repeated DataSource data_sources = 4;

  bool activation = 5;

  // The name of the instance.
  // Format: instances/{instance}
  string name = 6;

  // The environment resource.
  // Format: environments/prod where prod is the environment resource ID.
  string environment = 7;
}

message SASLConfig {
  oneof mechanism {
    KerberosConfig krb_config = 1;
  }
}

message KerberosConfig {
  string primary = 1;
  string instance = 2;
  string realm = 3;
  bytes keytab = 4;
  string kdc_host = 5;
  string kdc_port = 6;
  string kdc_transport_protocol = 7;
}
