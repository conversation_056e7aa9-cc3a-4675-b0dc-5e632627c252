syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "v1/annotation.proto";
import "v1/common.proto";
import "v1/iam_policy.proto";

option go_package = "generated-go/v1";

service AuditLogService {
  rpc SearchAuditLogs(SearchAuditLogsRequest) returns (SearchAuditLogsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/auditLogs:search"
      body: "*"
      additional_bindings: {
        post: "/v1/auditLogs:search"
        body: "*"
      }
    };
    option (bytebase.v1.permission) = "bb.auditLogs.search";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc ExportAuditLogs(ExportAuditLogsRequest) returns (ExportAuditLogsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/auditLogs:export"
      body: "*"
      additional_bindings: {
        post: "/v1/auditLogs:export"
        body: "*"
      }
    };
    option (bytebase.v1.permission) = "bb.auditLogs.export";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message SearchAuditLogsRequest {
  string parent = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/AuditLog"}
  ];

  // The filter of the log. It should be a valid CEL expression.
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // Supported filter:
  // - method: the API name, can be found in the docs, should start with "/bytebase.v1." prefix. For example "/bytebase.v1.UserService/CreateUser". Support "==" operator.
  // - severity: support "==" operator, check Severity enum in AuditLog message for values.
  // - user: the actor, should in "users/{email}" format, support "==" operator.
  // - create_time: support ">=" and "<=" operator.
  //
  // For example:
  //  - filter = "method == '/bytebase.v1.SQLService/Query'"
  //  - filter = "method == '/bytebase.v1.SQLService/Query' && severity == 'ERROR'"
  //  - filter = "method == '/bytebase.v1.SQLService/Query' && severity == 'ERROR' && user == 'users/<EMAIL>'"
  //  - filter = "method == '/bytebase.v1.SQLService/Query' && severity == 'ERROR' && create_time <= '2021-01-01T00:00:00Z' && create_time >= '2020-01-01T00:00:00Z'"
  string filter = 1;

  // The order by of the log.
  // Only support order by create_time.
  // For example:
  //  - order_by = "create_time asc"
  //  - order_by = "create_time desc"
  string order_by = 2;

  // The maximum number of logs to return.
  // The service may return fewer than this value.
  // If unspecified, at most 10 log entries will be returned.
  // The maximum value is 5000; values above 5000 will be coerced to 5000.
  int32 page_size = 3;

  // A page token, received from a previous `SearchLogs` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 4;
}

message SearchAuditLogsResponse {
  repeated AuditLog audit_logs = 1;

  // A token to retrieve next page of log entities.
  // Pass this value in the page_token field in the subsequent call
  // to retrieve the next page of log entities.
  string next_page_token = 2;
}

message ExportAuditLogsRequest {
  string parent = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/AuditLog"}
  ];

  // The filter of the log. It should be a valid CEL expression.
  // Check the filter field in the SearchAuditLogsRequest message.
  string filter = 1;

  // The order by of the log.
  // Only support order by create_time.
  // For example:
  //  - order_by = "create_time asc"
  //  - order_by = "create_time desc"
  string order_by = 2;

  // The export format.
  ExportFormat format = 3;

  // The maximum number of logs to return.
  // The service may return fewer than this value.
  // If unspecified, at most 10 log entries will be returned.
  // The maximum value is 5000; values above 5000 will be coerced to 5000.
  int32 page_size = 5;

  // A page token, received from a previous `ExportAuditLogs` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 6;
}

message ExportAuditLogsResponse {
  bytes content = 1;

  // A token to retrieve next page of log entities.
  // Pass this value in the page_token field in the subsequent call
  // to retrieve the next page of log entities.
  string next_page_token = 2;
}

message AuditLog {
  // The name of the log.
  // Formats:
  // - projects/{project}/auditLogs/{uid}
  // - workspaces/{workspace}/auditLogs/{uid}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Format: users/<EMAIL>
  string user = 3;

  // e.g. `/bytebase.v1.SQLService/Query`, `bb.project.repository.push`
  string method = 4;

  Severity severity = 5;

  // The associated resource.
  string resource = 6;

  // JSON-encoded request.
  string request = 7;

  // JSON-encoded response.
  // Some fields are omitted because they are too large or contain sensitive information.
  string response = 8;

  google.rpc.Status status = 9;

  // service-specific data about the request, response, and other activities.
  google.protobuf.Any service_data = 10;

  // Metadata about the operation.
  RequestMetadata request_metadata = 11;

  enum Severity {
    DEFAULT = 0;
    DEBUG = 1;
    INFO = 2;
    NOTICE = 3;
    WARNING = 4;
    ERROR = 5;
    CRITICAL = 6;
    ALERT = 7;
    EMERGENCY = 8;
  }
}

message AuditData {
  PolicyDelta policy_delta = 1;
}

// Metadata about the request.
message RequestMetadata {
  // The IP address of the caller.
  string caller_ip = 1;

  // The user agent of the caller.
  // This information is not authenticated and should be treated accordingly.
  string caller_supplied_user_agent = 2;
}
