syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/type/expr.proto";
import "v1/annotation.proto";
import "v1/common.proto";

option go_package = "generated-go/v1";

service OrgPolicyService {
  rpc GetPolicy(GetPolicyRequest) returns (Policy) {
    option (google.api.http) = {
      get: "/v1/{name=policies/*}"

      additional_bindings: {get: "/v1/{name=projects/*/policies/*}"}
      additional_bindings: {get: "/v1/{name=environments/*/policies/*}"}
      additional_bindings: {get: "/v1/{name=instances/*/policies/*}"}
      additional_bindings: {get: "/v1/{name=instances/*/databases/*/policies/*}"}
    };
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.policies.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListPolicies(ListPoliciesRequest) returns (ListPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/policies"

      additional_bindings: {get: "/v1/{parent=projects/*}/policies"}
      additional_bindings: {get: "/v1/{parent=environments/*}/policies"}
      additional_bindings: {get: "/v1/{parent=instances/*}/policies"}
      additional_bindings: {get: "/v1/{parent=instances/*/databases/*}/policies"}
    };
    option (google.api.method_signature) = "";
    option (bytebase.v1.permission) = "bb.policies.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc CreatePolicy(CreatePolicyRequest) returns (Policy) {
    option (google.api.http) = {
      post: "/v1/policies"
      body: "policy"

      additional_bindings: {
        post: "/v1/{parent=projects/*}/policies"
        body: "policy"
      }
      additional_bindings: {
        post: "/v1/{parent=environments/*}/policies"
        body: "policy"
      }
      additional_bindings: {
        post: "/v1/{parent=instances/*}/policies"
        body: "policy"
      }
      additional_bindings: {
        post: "/v1/{parent=instances/*/databases/*}/policies"
        body: "policy"
      }
    };
    option (google.api.method_signature) = "parent,policy";
    option (bytebase.v1.permission) = "bb.policies.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UpdatePolicy(UpdatePolicyRequest) returns (Policy) {
    option (google.api.http) = {
      patch: "/v1/{policy.name=policies/*}"
      body: "policy"

      additional_bindings: {
        patch: "/v1/{policy.name=projects/*/policies/*}"
        body: "policy"
      }
      additional_bindings: {
        patch: "/v1/{policy.name=environments/*/policies/*}"
        body: "policy"
      }
      additional_bindings: {
        patch: "/v1/{policy.name=instances/*/policies/*}"
        body: "policy"
      }
      additional_bindings: {
        patch: "/v1/{policy.name=instances/*/databases/*/policies/*}"
        body: "policy"
      }
    };
    option (google.api.method_signature) = "policy,update_mask";
    option (bytebase.v1.permission) = "bb.policies.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc DeletePolicy(DeletePolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=policies/*}"

      additional_bindings: {delete: "/v1/{name=projects/*/policies/*}"}
      additional_bindings: {delete: "/v1/{name=environments/*/policies/*}"}
      additional_bindings: {delete: "/v1/{name=instances/*/policies/*}"}
      additional_bindings: {delete: "/v1/{name=instances/*/databases/*/policies/*}"}
    };
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.policies.delete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
}

message CreatePolicyRequest {
  // The parent resource where this instance will be created.
  // Workspace resource name: "".
  // Environment resource name: environments/environment-id.
  // Instance resource name: instances/instance-id.
  // Database resource name: instances/instance-id/databases/database-name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/Policy"}
  ];

  // The policy to create.
  Policy policy = 2 [(google.api.field_behavior) = REQUIRED];

  PolicyType type = 3;
}

message UpdatePolicyRequest {
  // The policy to update.
  //
  // The policy's `name` field is used to identify the instance to update.
  // Format: {resource name}/policies/{policy type}
  // Workspace resource name: "".
  // Environment resource name: environments/environment-id.
  // Instance resource name: instances/instance-id.
  // Database resource name: instances/instance-id/databases/database-name.
  Policy policy = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the policy is not found, a new policy will be created.
  // In this situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

message DeletePolicyRequest {
  // The policy's `name` field is used to identify the instance to update.
  // Format: {resource name}/policies/{policy type}
  // Workspace resource name: "".
  // Environment resource name: environments/environment-id.
  // Instance resource name: instances/instance-id.
  // Database resource name: instances/instance-id/databases/database-name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Policy"}
  ];
}

message GetPolicyRequest {
  // The name of the policy to retrieve.
  // Format: {resource type}/{resource id}/policies/{policy type}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Policy"}
  ];
}

message ListPoliciesRequest {
  // The parent, which owns this collection of policies.
  // Format: {resource type}/{resource id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {child_type: "bytebase.com/Policy"}
  ];

  optional PolicyType policy_type = 2;

  // Not used.
  // The maximum number of policies to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 policies will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;

  // Not used.
  // A page token, received from a previous `ListPolicies` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListPolicies` must match
  // the call that provided the page token.
  string page_token = 4;

  // Show deleted policies if specified.
  bool show_deleted = 5;
}

message ListPoliciesResponse {
  // The policies from the specified request.
  repeated Policy policies = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message Policy {
  reserved 2;

  option (google.api.resource) = {
    type: "bytebase.com/Policy"
    pattern: "policies/{policy}"
    pattern: "projects/{project}/policies/{policy}"
    pattern: "environments/{environment}/policies/{policy}"
    pattern: "instances/{instance}/policies/{policy}"
    pattern: "instances/{instance}/databases/{database}/policies/{policy}"
  };

  // The name of the policy.
  // Format: {resource name}/policies/{policy type}
  // Workspace resource name: "".
  // Environment resource name: environments/environment-id.
  // Instance resource name: instances/instance-id.
  // Database resource name: instances/instance-id/databases/database-name.
  string name = 1;

  bool inherit_from_parent = 4;

  PolicyType type = 5;

  oneof policy {
    RolloutPolicy rollout_policy = 19;
    DisableCopyDataPolicy disable_copy_data_policy = 16;
    MaskingRulePolicy masking_rule_policy = 17;
    MaskingExceptionPolicy masking_exception_policy = 18;
    RestrictIssueCreationForSQLReviewPolicy restrict_issue_creation_for_sql_review_policy = 20;
    TagPolicy tag_policy = 21;
    DataSourceQueryPolicy data_source_query_policy = 22;
    ExportDataPolicy export_data_policy = 23;
    QueryDataPolicy query_data_policy = 24;
  }

  bool enforce = 13;

  // The resource type for the policy.
  PolicyResourceType resource_type = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

enum PolicyType {
  reserved 2, 4, 6, 5, 7;

  POLICY_TYPE_UNSPECIFIED = 0;
  ROLLOUT_POLICY = 11;
  DISABLE_COPY_DATA = 8;
  MASKING_RULE = 9;
  MASKING_EXCEPTION = 10;
  RESTRICT_ISSUE_CREATION_FOR_SQL_REVIEW = 12;
  TAG = 13;
  DATA_SOURCE_QUERY = 14;
  DATA_EXPORT = 15;
  DATA_QUERY = 16;
}

enum PolicyResourceType {
  RESOURCE_TYPE_UNSPECIFIED = 0;
  WORKSPACE = 1;
  ENVIRONMENT = 2;
  PROJECT = 3;
  INSTANCE = 4;
  DATABASE = 5;
}

message RolloutPolicy {
  bool automatic = 1;
  repeated string roles = 2;
  // roles/LAST_APPROVER
  // roles/CREATOR
  repeated string issue_roles = 3;
}

message DisableCopyDataPolicy {
  bool active = 1;
}

message ExportDataPolicy {
  bool disable = 1;
}

// QueryDataPolicy is the policy configuration for querying data.
message QueryDataPolicy {
  // The query timeout duration.
  google.protobuf.Duration timeout = 1;
}

message SQLReviewRule {
  string type = 1;
  SQLReviewRuleLevel level = 2;
  string payload = 3;
  Engine engine = 4;
  string comment = 5;
}

enum SQLReviewRuleLevel {
  LEVEL_UNSPECIFIED = 0;
  ERROR = 1;
  WARNING = 2;
  DISABLED = 3;
}

// MaskingExceptionPolicy is the allowlist of users who can access sensitive data.
message MaskingExceptionPolicy {
  message MaskingException {
    enum Action {
      ACTION_UNSPECIFIED = 0;
      QUERY = 1;
      EXPORT = 2;
    }
    // action is the action that the user can access sensitive data.
    Action action = 1;

    // Member is the principal who bind to this exception policy instance.
    //
    // - `user:{email}`: An email address that represents a specific Bytebase account. For example, `<EMAIL>`.
    // - `group:{email}`: An email address for group.
    string member = 3;

    // The condition that is associated with this exception policy instance.
    // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
    // If the condition is empty, means the user can access all databases without expiration.
    //
    // Support variables:
    // resource.instance_id: the instance resource id. Only support "==" operation.
    // resource.database_name: the database name. Only support "==" operation.
    // resource.schema_name: the schema name. Only support "==" operation.
    // resource.table_name: the table name. Only support "==" operation.
    // resource.column_name: the column name. Only support "==" operation.
    // request.time: the expiration. Only support "<" operation in `request.time < timestamp("{ISO datetime string format}")`
    // All variables should join with "&&" condition.
    //
    // For example:
    // resource.instance_id == "local" && resource.database_name == "employee" && request.time < timestamp("2025-04-30T11:10:39.000Z")
    // resource.instance_id == "local" && resource.database_name == "employee"
    google.type.Expr condition = 4;
  }

  repeated MaskingException masking_exceptions = 1;
}

message MaskingRulePolicy {
  message MaskingRule {
    // A unique identifier for a node in UUID format.
    string id = 1;

    // The condition for the masking rule.
    // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
    //
    // Support variables:
    // environment_id: the environment resource id.
    // project_id: the project resource id.
    // instance_id: the instance resource id.
    // database_name: the database name.
    // table_name: the table name.
    // column_name: the column name.
    // classification_level: the classification level.
    //
    // Each variable support following operations:
    // ==: the value equals the target.
    // !=: the value not equals the target.
    // in: the value matches one of the targets.
    // !(in): the value not matches any of the targets.
    //
    // For example:
    // environment_id == "test" && project_id == "sample-project"
    // instance_id == "sample-instance" && database_name == "employee" && table_name in ["table1", "table2"]
    // environment_id != "test" || !(project_id in ["poject1", "prject2"])
    // instance_id == "sample-instance" && (database_name == "db1" || database_name == "db2")
    google.type.Expr condition = 2;

    string semantic_type = 3;
  }
  repeated MaskingRule rules = 1;
}

message RestrictIssueCreationForSQLReviewPolicy {
  bool disallow = 1;
}

message TagPolicy {
  // tags is the key - value map for resources.
  // for example, the environment resource can have the sql review config tag, like "bb.tag.review_config": "reviewConfigs/{review config resource id}"
  map<string, string> tags = 1;
}

// DataSourceQueryPolicy is the policy configuration for running statements in the SQL editor.
message DataSourceQueryPolicy {
  enum Restriction {
    RESTRICTION_UNSPECIFIED = 0;
    // Allow to query admin data sources when there is no read-only data source.
    FALLBACK = 1;
    // Disallow to query admin data sources.
    DISALLOW = 2;
  }
  Restriction admin_data_source_restriction = 1;

  // Disallow running DDL statements in the SQL editor.
  bool disallow_ddl = 2;
  // Disallow running DML statements in the SQL editor.
  bool disallow_dml = 3;
}
