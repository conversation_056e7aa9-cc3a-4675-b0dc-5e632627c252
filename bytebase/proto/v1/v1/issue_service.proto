syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/expr.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service IssueService {
  rpc GetIssue(GetIssueRequest) returns (Issue) {
    option (google.api.http) = {get: "/v1/{name=projects/*/issues/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.issues.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc CreateIssue(CreateIssueRequest) returns (Issue) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issues"
      body: "issue"
    };
    option (google.api.method_signature) = "parent,issue";
    // XXX: issues.action needs respective plans.action and rollouts.action permissions if the issue type is change database.
    option (bytebase.v1.permission) = "bb.issues.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc ListIssues(ListIssuesRequest) returns (ListIssuesResponse) {
    option (google.api.http) = {get: "/v1/{parent=projects/*}/issues"};
    option (google.api.method_signature) = "parent";
    option (bytebase.v1.permission) = "bb.issues.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  // Search for issues that the caller has the bb.issues.get permission on and also satisfy the specified filter & query.
  rpc SearchIssues(SearchIssuesRequest) returns (SearchIssuesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issues:search"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.issues.get";
    option (bytebase.v1.auth_method) = CUSTOM;
  }

  rpc UpdateIssue(UpdateIssueRequest) returns (Issue) {
    option (google.api.http) = {
      patch: "/v1/{issue.name=projects/*/issues/*}"
      body: "issue"
    };
    option (google.api.method_signature) = "issue,update_mask";
    option (bytebase.v1.permission) = "bb.issues.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc ListIssueComments(ListIssueCommentsRequest) returns (ListIssueCommentsResponse) {
    option (google.api.http) = {get: "/v1/{parent=projects/*/issues/*}/issueComments"};
    option (google.api.method_signature) = "parent";
    option (bytebase.v1.permission) = "bb.issueComments.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc CreateIssueComment(CreateIssueCommentRequest) returns (IssueComment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/issues/*}:comment"
      body: "issue_comment"
    };
    option (google.api.method_signature) = "parent,issue_comment";
    option (bytebase.v1.permission) = "bb.issueComments.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc UpdateIssueComment(UpdateIssueCommentRequest) returns (IssueComment) {
    option (google.api.http) = {
      patch: "/v1/{parent=projects/*/issues/*}:comment"
      body: "issue_comment"
    };
    option (google.api.method_signature) = "parent,issue_comment,update_mask";
    option (bytebase.v1.permission) = "bb.issueComments.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  rpc BatchUpdateIssuesStatus(BatchUpdateIssuesStatusRequest) returns (BatchUpdateIssuesStatusResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issues:batchUpdateStatus"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.issues.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }

  // ApproveIssue approves the issue.
  // The access is based on approval flow.
  rpc ApproveIssue(ApproveIssueRequest) returns (Issue) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/issues/*}:approve"
      body: "*"
    };
    option (bytebase.v1.auth_method) = CUSTOM;
    option (bytebase.v1.audit) = true;
  }

  // RejectIssue rejects the issue.
  // The access is based on approval flow.
  rpc RejectIssue(RejectIssueRequest) returns (Issue) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/issues/*}:reject"
      body: "*"
    };
    option (bytebase.v1.auth_method) = CUSTOM;
    option (bytebase.v1.audit) = true;
  }

  // RequestIssue requests the issue.
  // The access is based on approval flow.
  rpc RequestIssue(RequestIssueRequest) returns (Issue) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/issues/*}:request"
      body: "*"
    };
    option (bytebase.v1.auth_method) = CUSTOM;
    option (bytebase.v1.audit) = true;
  }
}

message GetIssueRequest {
  // The name of the issue to retrieve.
  // Format: projects/{project}/issues/{issue}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];

  bool force = 2;
}

message CreateIssueRequest {
  // The parent, which owns this collection of issues.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The issue to create.
  Issue issue = 2 [(google.api.field_behavior) = REQUIRED];
}

message ListIssuesRequest {
  // The parent, which owns this collection of issues.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The maximum number of issues to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 issues will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListIssues` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListIssues` must match
  // the call that provided the page token.
  string page_token = 3;

  // Filter is used to filter issues returned in the list.
  // The syntax and semantics of CEL are documented at https://github.com/google/cel-spec
  //
  // Supported filters:
  // - creator: issue creator full name in "users/{email or id}" format, support "==" operator.
  // - subscriber: issue subscriber full name in "users/{email or id}" format, support "==" operator.
  // - status: the issue status, support "==" and "in" operator, check the IssueStatus enum for the values.
  // - create_time: issue create time in "2006-01-02T15:04:05Z07:00" format, support ">=" or "<=" operator.
  // - type: the issue type, support "==" and "in" operator, check the Type enum in the Issue message for the values.
  // - task_type: support "==" operator, the value can be "DDL", "DML" or "DATA_EXPORT"
  // - instance: the instance full name in the "instances/{id}" format, support "==" operator.
  // - database: the database full name in the "instances/{id}/databases/{name}" format, support "==" operator.
  // - labels: the issue labels, support "==" and "in" operator.
  // - has_pipeline: the issue has pipeline or not, support "==" operator, the value should be "true" or "false".
  //
  // For example:
  // creator == "users/<EMAIL>" && status in ["OPEN", "DONE"]
  // status == "CANCELED" && type == "DATABASE_CHANGE"
  // instance == "instances/sample" && labels in ["label1", "label2"]
  // has_pipeline == true && create_time >= "2025-01-02T15:04:05Z07:00"
  string filter = 4;

  // Query is the query statement.
  string query = 5;
}

message ListIssuesResponse {
  // The issues from the specified request.
  repeated Issue issues = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message SearchIssuesRequest {
  // The parent, which owns this collection of issues.
  // Format: projects/{project}
  // Use "projects/-" to list all issues from all projects.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of issues to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 issues will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `SearchIssues` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `SearchIssues` must match
  // the call that provided the page token.
  string page_token = 3;

  // Filter is used to filter issues returned in the list.
  // Check the filter field in the ListIssuesRequest message.
  string filter = 4;

  // Query is the query statement.
  string query = 5;
}

message SearchIssuesResponse {
  // The issues from the specified request.
  repeated Issue issues = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message UpdateIssueRequest {
  // The issue to update.
  //
  // The issue's `name` field is used to identify the issue to update.
  // Format: projects/{project}/issues/{issue}
  Issue issue = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchUpdateIssuesStatusRequest {
  // The parent resource shared by all issues being updated.
  // Format: projects/{project}
  // If the operation spans parents, a dash (-) may be accepted as a wildcard.
  // We only support updating the status of databases for now.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The list of issues to update.
  // Format: projects/{project}/issues/{issue}
  repeated string issues = 2;

  // The new status.
  IssueStatus status = 3;

  string reason = 4;
}

message BatchUpdateIssuesStatusResponse {}

message ApproveIssueRequest {
  // The name of the issue to add an approver.
  // Format: projects/{project}/issues/{issue}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];
  string comment = 2;
}

message RejectIssueRequest {
  // The name of the issue to add an rejection.
  // Format: projects/{project}/issues/{issue}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];
  string comment = 2;
}

message RequestIssueRequest {
  // The name of the issue to request a issue.
  // Format: projects/{project}/issues/{issue}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];
  string comment = 2;
}

message Issue {
  option (google.api.resource) = {
    type: "bytebase.com/Issue"
    pattern: "projects/{project}/issues/{issue}"
  };

  reserved 2, 7, 8;

  // The name of the issue.
  // Format: projects/{project}/issues/{issue}
  string name = 1;

  string title = 3;

  string description = 4;

  enum Type {
    TYPE_UNSPECIFIED = 0;
    DATABASE_CHANGE = 1;
    GRANT_REQUEST = 2;
    DATABASE_DATA_EXPORT = 3;
  }
  Type type = 5;

  IssueStatus status = 6;

  message Approver {
    enum Status {
      STATUS_UNSPECIFIED = 0;
      PENDING = 1;
      APPROVED = 2;
      REJECTED = 3;
    }
    // The new status.
    Status status = 1;

    // Format: users/<EMAIL>
    string principal = 2;
  }
  repeated Approver approvers = 9;

  repeated ApprovalTemplate approval_templates = 10;

  // If the value is `false`, it means that the backend is still finding matching approval templates.
  // If `true`, approval_templates & approvers & approval_finding_error are available.
  bool approval_finding_done = 11;
  string approval_finding_error = 12;

  // The subscribers.
  // Format: users/<EMAIL>
  repeated string subscribers = 13;

  // Format: users/<EMAIL>
  string creator = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp create_time = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp update_time = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The plan associated with the issue.
  // Can be empty.
  // Format: projects/{project}/plans/{plan}
  string plan = 17;

  // The rollout associated with the issue.
  // Can be empty.
  // Format: projects/{project}/rollouts/{rollout}
  string rollout = 18;

  // Used if the issue type is GRANT_REQUEST.
  GrantRequest grant_request = 19;

  // The releasers of the pending stage of the issue rollout, judging
  // from the rollout policy.
  // Format:
  // - roles/workspaceOwner
  // - roles/workspaceDBA
  // - roles/projectOwner
  // - roles/projectReleaser
  // - users/{email}
  repeated string releasers = 20;

  enum RiskLevel {
    RISK_LEVEL_UNSPECIFIED = 0;
    LOW = 1;
    MODERATE = 2;
    HIGH = 3;
  }
  RiskLevel risk_level = 21;

  // The status count of the issue.
  // Keys are the following:
  // - NOT_STARTED
  // - SKIPPED
  // - PENDING
  // - RUNNING
  // - DONE
  // - FAILED
  // - CANCELED
  map<string, int32> task_status_count = 22;

  repeated string labels = 23;
}

message GrantRequest {
  // The requested role.
  // Format: roles/EXPORTER.
  string role = 1;
  // The user to be granted.
  // Format: users/{email}.
  string user = 2;
  // The condition for the role. Same as the condtion in IAM Binding message.
  google.type.Expr condition = 3;
  google.protobuf.Duration expiration = 4;
}

enum IssueStatus {
  ISSUE_STATUS_UNSPECIFIED = 0;
  OPEN = 1;
  DONE = 2;
  CANCELED = 3;
}

message ApprovalTemplate {
  ApprovalFlow flow = 1;
  string title = 2;
  string description = 3;
}

message ApprovalFlow {
  repeated ApprovalStep steps = 1;
}

message ApprovalStep {
  // Type of the ApprovalStep
  // ALL means every node must be approved to proceed.
  // ANY means approving any node will proceed.
  enum Type {
    TYPE_UNSPECIFIED = 0;
    ALL = 1;
    ANY = 2;
  }
  Type type = 1;

  repeated ApprovalNode nodes = 2;
}

message ApprovalNode {
  // Type of the ApprovalNode.
  // type determines who should approve this node.
  // ANY_IN_GROUP means the ApprovalNode can be approved by an user from our predefined user group.
  // See GroupValue below for the predefined user groups.
  enum Type {
    TYPE_UNSPECIFIED = 0;
    ANY_IN_GROUP = 1;
  }
  Type type = 1;

  string role = 2;
}

message ListIssueCommentsRequest {
  // Format: projects/{projects}/issues/{issue}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];

  // The maximum number of issue comments to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 issue comments will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListIssueComments` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListIssueComments` must match
  // the call that provided the page token.
  string page_token = 3;
}

message ListIssueCommentsResponse {
  repeated IssueComment issue_comments = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateIssueCommentRequest {
  // The issue name
  // Format: projects/{project}/issues/{issue}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];

  IssueComment issue_comment = 2;
}

message UpdateIssueCommentRequest {
  // The issue name
  // Format: projects/{project}/issues/{issue}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Issue"}
  ];

  IssueComment issue_comment = 2;

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];
}

message IssueComment {
  reserved 6;

  // Format: projects/{project}/issues/{issue}/issueComments/{issueComment-uid}
  string name = 1;

  string comment = 2;

  // TODO: use struct message instead.
  string payload = 3;

  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Format: users/{email}
  string creator = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof event {
    Approval approval = 8;
    IssueUpdate issue_update = 9;
    StageEnd stage_end = 10;
    TaskUpdate task_update = 11;
    TaskPriorBackup task_prior_backup = 12;
  }

  message Approval {
    Status status = 1;

    enum Status {
      STATUS_UNSPECIFIED = 0;
      PENDING = 1;
      APPROVED = 2;
      REJECTED = 3;
    }
  }

  message IssueUpdate {
    reserved 7, 8;

    optional string from_title = 1;
    optional string to_title = 2;
    optional string from_description = 3;
    optional string to_description = 4;
    optional IssueStatus from_status = 5;
    optional IssueStatus to_status = 6;

    repeated string from_labels = 9;
    repeated string to_labels = 10;
  }

  message StageEnd {
    string stage = 1;
  }

  message TaskUpdate {
    repeated string tasks = 1;
    // Format: projects/{project}/sheets/{sheet}
    optional string from_sheet = 2;
    // Format: projects/{project}/sheets/{sheet}
    optional string to_sheet = 3;
    optional google.protobuf.Timestamp from_earliest_allowed_time = 4;
    optional google.protobuf.Timestamp to_earliest_allowed_time = 5;
    optional Status to_status = 6;

    enum Status {
      STATUS_UNSPECIFIED = 0;
      PENDING = 1;
      RUNNING = 2;
      DONE = 3;
      FAILED = 4;
      SKIPPED = 5;
      CANCELED = 6;
    }
  }

  message TaskPriorBackup {
    string task = 1;
    repeated Table tables = 2;
    optional int32 original_line = 3;
    string database = 4;
    string error = 5;

    message Table {
      string schema = 1;
      string table = 2;
    }
  }
}
