syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service RoleService {
  rpc ListRoles(ListRolesRequest) returns (ListRolesResponse) {
    option (google.api.http) = {get: "/v1/roles"};
    option (bytebase.v1.permission) = "bb.roles.list";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc GetRole(GetRoleRequest) returns (Role) {
    option (google.api.http) = {get: "/v1/{name=roles/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.roles.get";
    option (bytebase.v1.auth_method) = IAM;
  }
  rpc CreateRole(CreateRoleRequest) returns (Role) {
    option (google.api.http) = {
      post: "/v1/roles"
      body: "role"
    };
    option (bytebase.v1.permission) = "bb.roles.create";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
  rpc UpdateRole(UpdateRoleRequest) returns (Role) {
    option (google.api.http) = {
      patch: "/v1/{role.name=roles/*}"
      body: "role"
    };
    option (google.api.method_signature) = "role,update_mask";
    option (bytebase.v1.permission) = "bb.roles.update";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
  rpc DeleteRole(DeleteRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=roles/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.roles.delete";
    option (bytebase.v1.auth_method) = IAM;
    option (bytebase.v1.audit) = true;
  }
}

message ListRolesRequest {
  // Not used.
  // The maximum number of roles to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 reviews will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 1;

  // Not used.
  // A page token, received from a previous `ListRoles` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRoles` must match
  // the call that provided the page token.
  string page_token = 2;
}

message ListRolesResponse {
  repeated Role roles = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message CreateRoleRequest {
  Role role = 1 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the role, which will become the final component
  // of the role's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][A-Z][0-9]/.
  string role_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message GetRoleRequest {
  // The name of the role to retrieve.
  // Format: roles/{role}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Role"}
  ];
}

message UpdateRoleRequest {
  Role role = 1;
  google.protobuf.FieldMask update_mask = 2;
  // If set to true, and the role is not found, a new role will be created.
  bool allow_missing = 3;
}

message DeleteRoleRequest {
  // Format: roles/{role}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Role"}
  ];
}

message Role {
  option (google.api.resource) = {
    type: "bytebase.com/Role"
    pattern: "roles/{role}"
  };

  // Format: roles/{role}
  string name = 1;
  string title = 2;
  string description = 3;
  repeated string permissions = 4;

  enum Type {
    TYPE_UNSPECIFIED = 0;
    BUILT_IN = 1;
    CUSTOM = 2;
  }

  Type type = 5;
}
