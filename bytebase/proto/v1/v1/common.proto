syntax = "proto3";

package bytebase.v1;

option go_package = "generated-go/v1";

enum State {
  STATE_UNSPECIFIED = 0;
  ACTIVE = 1;
  DELETED = 2;
}

enum Engine {
  ENGINE_UNSPECIFIED = 0;
  CLICKHOUSE = 1;
  MYSQL = 2;
  POSTGRES = 3;
  SNOWFLAKE = 4;
  SQLITE = 5;
  TIDB = 6;
  MONGODB = 7;
  REDIS = 8;
  ORACLE = 9;
  SPANNER = 10;
  MSSQL = 11;
  REDSHIFT = 12;
  MARIADB = 13;
  OCEANBASE = 14;
  DM = 15;
  RISINGWAVE = 16;
  OCEANBASE_ORACLE = 17;
  STARROCKS = 18;
  DORIS = 19;
  HIVE = 20;
  ELASTICSEARCH = 21;
  BIGQUERY = 22;
  DYNAMODB = 23;
  DATABRICKS = 24;
  COCKROACHDB = 25;
  COSMOSDB = 26;
  TRINO = 27;
  CASSANDRA = 28;
}

enum VCSType {
  VCS_TYPE_UNSPECIFIED = 0;
  GITHUB = 1;
  GITLAB = 2;
  BITBUCKET = 3;
  AZURE_DEVOPS = 4;
}

enum ExportFormat {
  FORMAT_UNSPECIFIED = 0;
  CSV = 1;
  JSON = 2;
  SQL = 3;
  XLSX = 4;
}

// Position in a text expressed as zero-based line and zero-based column byte
// offset.
message Position {
  // Line position in a text (zero-based).
  int32 line = 1;
  // Column position in a text (zero-based), equivalent to byte offset.
  int32 column = 2;
}

message Range {
  int32 start = 1;
  int32 end = 2;
}
