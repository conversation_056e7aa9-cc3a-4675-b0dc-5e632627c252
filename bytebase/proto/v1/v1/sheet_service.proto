syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "v1/annotation.proto";
import "v1/common.proto";

option go_package = "generated-go/v1";

service SheetService {
  rpc CreateSheet(CreateSheetRequest) returns (Sheet) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/sheets"
      body: "sheet"
    };
    option (google.api.method_signature) = "parent,sheet";
    option (bytebase.v1.permission) = "bb.sheets.create";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc BatchCreateSheet(BatchCreateSheetRequest) returns (BatchCreateSheetResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/sheets:batchCreate"
      body: "*"
    };
    option (bytebase.v1.permission) = "bb.sheets.create";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc GetSheet(GetSheetRequest) returns (Sheet) {
    option (google.api.http) = {get: "/v1/{name=projects/*/sheets/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.sheets.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc UpdateSheet(UpdateSheetRequest) returns (Sheet) {
    option (google.api.http) = {
      patch: "/v1/{sheet.name=projects/*/sheets/*}"
      body: "sheet"
    };
    option (google.api.method_signature) = "sheet,update_mask";
    option (bytebase.v1.permission) = "bb.sheets.update";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message CreateSheetRequest {
  // The parent resource where this sheet will be created.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The sheet to create.
  Sheet sheet = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchCreateSheetRequest {
  // The parent resource where all sheets will be created.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  repeated CreateSheetRequest requests = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchCreateSheetResponse {
  repeated Sheet sheets = 1;
}

message GetSheetRequest {
  // The name of the sheet to retrieve.
  // Format: projects/{project}/sheets/{sheet}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Sheet"}
  ];

  // By default, the content of the sheet is cut off, set the `raw` to true to retrieve the full content.
  bool raw = 2;
}

message UpdateSheetRequest {
  // The sheet to update.
  //
  // The sheet's `name` field is used to identify the sheet to update.
  // Format: projects/{project}/sheets/{sheet}
  Sheet sheet = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // Fields are specified relative to the sheet.
  // (e.g. `title`, `statement`; *not* `sheet.title` or `sheet.statement`)
  // Only support update the following fields for now:
  // - `title`
  // - `statement`
  google.protobuf.FieldMask update_mask = 2;
}

message Sheet {
  option (google.api.resource) = {
    type: "bytebase.com/Sheet"
    pattern: "projects/{project}/sheets/{sheet}"
  };

  // The name of the sheet resource, generated by the server.
  // Canonical parent is project.
  // Format: projects/{project}/sheets/{sheet}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The title of the sheet.
  string title = 3 [(google.api.field_behavior) = REQUIRED];

  // The creator of the Sheet.
  // Format: users/{email}
  string creator = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The create time of the sheet.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The content of the sheet.
  // By default, it will be cut off, if it doesn't match the `content_size`, you can
  // set the `raw` to true in GetSheet request to retrieve the full content.
  bytes content = 7 [(google.api.field_behavior) = REQUIRED];

  // content_size is the full size of the content, may not match the size of the `content` field.
  int64 content_size = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  SheetPayload payload = 13;

  // The SQL dialect.
  Engine engine = 14 [(google.api.field_behavior) = REQUIRED];
}

message SheetPayload {
  // Type of the SheetPayload.
  enum Type {
    TYPE_UNSPECIFIED = 0;
    SCHEMA_DESIGN = 1;
  }
  Type type = 1;

  // The start and end position of each command in the sheet statement.
  repeated SheetCommand commands = 4;
}

message SheetCommand {
  int32 start = 1;
  int32 end = 2;
}
