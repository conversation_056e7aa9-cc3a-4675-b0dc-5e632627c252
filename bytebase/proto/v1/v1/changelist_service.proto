syntax = "proto3";

package bytebase.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "v1/annotation.proto";

option go_package = "generated-go/v1";

service ChangelistService {
  rpc CreateChangelist(CreateChangelistRequest) returns (Changelist) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/changelists"
      body: "changelist"
    };
    option (google.api.method_signature) = "parent,changelist";
    option (bytebase.v1.permission) = "bb.changelists.create";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc GetChangelist(GetChangelistRequest) returns (Changelist) {
    option (google.api.http) = {get: "/v1/{name=projects/*/changelists/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.changelists.get";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc ListChangelists(ListChangelistsRequest) returns (ListChangelistsResponse) {
    option (google.api.http) = {get: "/v1/{parent=projects/*}/changelists"};
    option (google.api.method_signature) = "parent";
    option (bytebase.v1.permission) = "bb.changelists.list";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc UpdateChangelist(UpdateChangelistRequest) returns (Changelist) {
    option (google.api.http) = {
      patch: "/v1/{changelist.name=projects/*/changelists/*}"
      body: "changelist"
    };
    option (google.api.method_signature) = "changelist,update_mask";
    option (bytebase.v1.permission) = "bb.changelists.update";
    option (bytebase.v1.auth_method) = IAM;
  }

  rpc DeleteChangelist(DeleteChangelistRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/v1/{name=projects/*/changelists/*}"};
    option (google.api.method_signature) = "name";
    option (bytebase.v1.permission) = "bb.changelists.delete";
    option (bytebase.v1.auth_method) = IAM;
  }
}

message CreateChangelistRequest {
  // The parent resource where this changelist will be created.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // The changelist to create.
  Changelist changelist = 2 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the changelist, which will become the final component of
  // the changelist's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  string changelist_id = 3 [(google.api.field_behavior) = REQUIRED];
}

message GetChangelistRequest {
  // The name of the changelist to retrieve.
  // Format: projects/{project}/changelists/{changelist}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Changelist"}
  ];
}

message ListChangelistsRequest {
  // The parent, which owns this collection of changelists.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Project"}
  ];

  // Not used.
  // The maximum number of databases to return. The service may return fewer than
  // this value.
  // If unspecified, at most 50 databases will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // Not used.
  // A page token, received from a previous `ListDatabases` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDatabases` must match
  // the call that provided the page token.
  string page_token = 3;
}

message ListChangelistsResponse {
  // The changelists from the specified request.
  repeated Changelist changelists = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

message UpdateChangelistRequest {
  // The changelist to update.
  //
  // The changelist's `name` field is used to identify the changelist to update.
  // Format: projects/{project}/changelists/{changelist}
  Changelist changelist = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

message DeleteChangelistRequest {
  // The name of the changelist to delete.
  // Format: projects/{project}/changelists/{changelist}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {type: "bytebase.com/Changelist"}
  ];
}

message Changelist {
  option (google.api.resource) = {
    type: "bytebase.com/Changelist"
    pattern: "projects/{project}/changelists/{changelist}"
  };

  // The name of the changelist resource.
  // Canonical parent is project.
  // Format: projects/{project}/changelists/{changelist}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  string description = 2;

  // The creator of the changelist.
  // Format: users/{email}
  string creator = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The last update time of the changelist.
  google.protobuf.Timestamp update_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  message Change {
    // The name of a sheet.
    string sheet = 1;
    // The source of origin.
    // 1) changelog: instances/{instance}/databases/{database}/changelogs/{changelog}.
    // 2) raw SQL if empty.
    string source = 2;
    // The migration version for a change.
    string version = 3;
  }
  repeated Change changes = 7;
}
