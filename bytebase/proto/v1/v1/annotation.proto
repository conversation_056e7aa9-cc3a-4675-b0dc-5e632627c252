syntax = "proto3";

package bytebase.v1;

import "google/protobuf/descriptor.proto";

option go_package = "generated-go/v1";

extend google.protobuf.MethodOptions {
  bool allow_without_credential = 100000;
  string permission = 100001;
  AuthMethod auth_method = 100002;
  bool audit = 100003;
}

enum AuthMethod {
  AUTH_METHOD_UNSPECIFIED = 0;
  // IAM uses the standard IAM authorization check on the organizational resources.
  IAM = 1;
  // Custom authorization method.
  CUSTOM = 2;
}
