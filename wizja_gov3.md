https://github.com/json-iterator/go
<PERSON><PERSON>hain for Go, the easiest way to write LLM-based programs in Go
https://github.com/tmc/langchaingo
chromem-go - Embeddable vector database for Go with Chroma-like interface and zero third-party dependencies. In-memory with optional persistence.
https://github.com/philippgille/chromem-go

Manage, load-balance, and failover packs of Ollamas
https://github.com/presbrey/ollamafarm




db 
https://github.com/hypermodeinc/dgraph Scalable, Distributed, Low Latency, High Throughput Graph Database.

https://github.com/DiceDB/dice  An open-source, fast, reactive, in-memory database optimized for modern hardware. Higher throughput and lower median latencies, making it ideal for modern workloads.
https://github.com/krotik/eliasdb

https://github.com/nalgeon/redka Redka aims to reimplement the core parts of Redis with SQLite, while remaining compatible with Redis API.

https://github.com/pressly/goose Goose is a database migration tool. Both a CLI and a library.

Manage your database schema by creating incremental SQL changes or Go functions.

Goose is a database migration tool. Both a CLI and a library.



Manage your database schema by creating incremental SQL changes or Go functions.

https://github.com/jackc/pgx pgx is a pure Go driver and toolkit for PostgreSQL.



o to chyba bardzo ważne : 
Capillaries is a data processing framework that:
https://github.com/capillariesio/capillaries

addresses scalability issues and manages intermediate data storage, enabling users to concentrate on data transforms and quality control;
bridges the gap between distributed, scalable data processing/integration solutions and the necessity to produce enriched, customer-ready, production-quality, human-curated data within SLA time limits.

https://github.com/emperror/emperror
The Emperor takes care of all errors personally.

Go's philosophy encourages to gracefully handle errors whenever possible, but some times recovering from an error is not.

IF kratos  dont have this : 
https://github.com/xis/baraka a tool for handling file uploads for http servers

makes it easier to make operations with files from the http request.

https://github.com/TeaEntityLab/fpGo


https://github.com/qax-os/excelize
https://github.com/gomutex/godocx

https://github.com/danieldk/go2vec

https://github.com/albrow/zoom
A blazing-fast datastore and querying engine for Go built on Redis.

https://github.com/vdobler/chart