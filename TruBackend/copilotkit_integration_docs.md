React integration, AI agents, backend setup, hooks"
tokens
15000
output
            TITLE: Implementing useCoAgent Hook for State Management
DESCRIPTION: Integration of useCoAgent hook to create bidirectional state connection between LangGraph agent and React application.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-4-integrate-the-agent.mdx#2025-04-23_snippet_3

LANGUAGE: tsx
CODE:
```
const { state, setState } = useCoAgent<AgentState>({
    name: "travel",
    initialState: {
      trips: defaultTrips,
      selected_trip_id: defaultTrips[0].id,
    },
  });
```

----------------------------------------

TITLE: Rendering Agent State with useCoAgent in React (TypeScript)
DESCRIPTION: This snippet demonstrates how to integrate the `useCoAgent` hook from `@copilotkit/react-core` into a React component to display the output state of a CrewAI agent. It defines a type for the agent's state, initializes the hook with the agent's name and an initial output, and then renders the agent's `outputs` using a `MarkdownRenderer` component.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-crews/generative-ui/agentic.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
// ...
import { useCoAgent } from "@copilotkit/react-core";
// ...

// Define the state of the agent, should match the state of the agent.
type AgentState = {
  inputs: {
    topic: string,
    current_year: string,
  },
  outputs: string,
};

function YourMainContent() {
  // ...

  // [!code highlight:14]
  // styles omitted for brevity
  const { state } = useCoAgent<AgentState>({
    name: "research_crew",
    initialState: {
      outputs: "Report will appear here",
    },
  });

  return (
    <div
        id="result"
      >
        <MarkdownRenderer content={state.outputs} />
      </div>
  )
}
```

----------------------------------------

TITLE: Rendering Agent Progress with useCoAgent and useCoAgentStateRender in React
DESCRIPTION: This snippet demonstrates how to use CoPilotKit's React hooks to render an AI agent's progress. It uses useCoAgent to access the agent's state and useCoAgentStateRender to display real-time progress updates.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/shared-state/predictive-state-updates.mdx#2025-04-23_snippet_6

LANGUAGE: tsx
CODE:
```
import { useCoAgent, useCoAgentStateRender } from '@copilotkit/react-core';

// ...
type AgentState = {
    observed_steps: string[];
};

const YourMainContent = () => {
    // Get access to both predicted and final states
    const { state } = useCoAgent<AgentState>({ name: "sample_agent" });

    // Add a state renderer to observe predictions
    useCoAgentStateRender({
        name: "sample_agent",
        render: ({ state }) => {
            if (!state.observed_steps?.length) return null;
            return (
                <div>
                    <h3>Current Progress:</h3>
                    <ul>
                        {state.observed_steps.map((step, i) => (
                            <li key={i}>{step}</li>
                        ))}
                    </ul>
                </div>
            );
        },
    });

    return (
        <div>
            <h1>Agent Progress</h1>
            {state.observed_steps?.length > 0 && (
                <div>
                    <h3>Final Steps:</h3>
                    <ul>
                        {state.observed_steps.map((step, i) => (
                            <li key={i}>{step}</li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    )
}
```

----------------------------------------

TITLE: Using Agent State in React Component
DESCRIPTION: Demonstrates how to use the agent state in a React component using the useCoAgent hook from @copilotkit/react-core.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/shared-state/state-inputs-outputs.mdx#2025-04-23_snippet_4

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  question: string;
  answer: string;
}

const { state } = useCoAgent<AgentState>({
  name: "sample_agent",
  initialState: {
    question: "How's is the weather in SF?",
  }
});

console.log(state) // You can expect seeing "answer" change, while the others are not returned from the agent
```

----------------------------------------

TITLE: Using useCoAgent Hook in React
DESCRIPTION: Demonstrates how to use the useCoAgent hook to access and display agent state in a React component, including type definitions and initial state setup.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/shared-state/in-app-agent-read.mdx#2025-04-23_snippet_1

LANGUAGE: typescript
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  language: "english" | "spanish";
}

function YourMainContent() {
  const { state } = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: { language: "spanish" }
  });

  return (
    <div>
      <h1>Your main content</h1>
      <p>Language: {state.language}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Rendering Agent State Outside React Chat Component
DESCRIPTION: This snippet demonstrates how to use the useCoAgent hook to render the state of an agent outside of the chat component in a React application.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/generative-ui/agentic.mdx#2025-04-23_snippet_5

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  searches: {
    query: string;
    done: boolean;
  }[];
};

function YourMainContent() {
  const { state } = useCoAgent<AgentState>({
    name: "sample_agent", // the name the agent is served as
  })

  return (
    <div>
      {/* ... */}
      <div className="flex flex-col gap-2 mt-4">
        {state.searches?.map((search, index) => (
          <div key={index} className="flex flex-row">
            {search.done ? "✅" : "❌"} {search.query}
          </div>
        ))}
      </div>
    </div>
  )
}
```

----------------------------------------

TITLE: Implementing Frontend Action with useCopilotAction in React
DESCRIPTION: Demonstrates how to create a frontend action using the useCopilotAction hook to enable an AI agent to trigger UI alerts. The action includes parameter definition and handler implementation.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/frontend-actions.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCopilotAction } from "@copilotkit/react-core"

export function Page() {
  useCopilotAction({
    name: "sayHello",
    description: "Say hello to the user",
    available: "remote",
    parameters: [
      {
        name: "name",
        type: "string",
        description: "The name of the user to say hello to",
        required: true,
      },
    ],
    handler: async ({ name }) => {
      alert(`Hello, ${name}!`);
    },
  });
}
```

----------------------------------------

TITLE: Using useCopilotAdditionalInstructions hook in React
DESCRIPTION: Demonstrates how to add custom instructions to an AI assistant using the useCopilotAdditionalInstructions hook in a React component with CopilotKit and CopilotPopup.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/guides/custom-ai-assistant-behavior.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { CopilotKit, useCopilotAdditionalInstructions } from "@copilotkit/react-core";
import { CopilotPopup } from "@copilotkit/react-ui"
 
function Chat() {
  useCopilotAdditionalInstructions({
    instructions: "Do not answer questions about the weather.",
  });
  return <CopilotPopup />
}

export function Home() {
  return (
    <CopilotKit>
      <Chat />
    </CopilotKit>
  )
}
```

----------------------------------------

TITLE: Initializing and Rendering CrewAI Agent State with CopilotKit (React, TypeScript)
DESCRIPTION: This TypeScript React (TSX) snippet demonstrates how to initialize a CrewAI agent with CopilotKit, rendering realtime step-by-step progress, handling text-based user inputs, and responding to agent feedback requests. It leverages CopilotKit's core agent and UI hooks, utilizes React state/effects for controlled UIs, and provides clear points for extension. Dependencies include CopilotKit React Core, CopilotKit Runtime Client GraphQL, and React. Inputs are the agent name and initial inputs array; outputs include real-time UI components reflecting agent state and user feedback. Constraints: the code assumes use with TypeScript and functional React components.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/snippets/crew-quickstart.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
"use client";

import {
  CrewsAgentState,
  CrewsResponseStatus,
  CrewsStateItem,
  CrewsTaskStateItem,
  CrewsToolStateItem,
  useCoAgent,
  useCoAgentStateRender,
  useCopilotAction,
  useCopilotChat,
  useCopilotAdditionalInstructions,
} from "@copilotkit/react-core";
import { useEffect, useMemo, useRef, useState } from "react";

import { MessageRole, TextMessage } from "@copilotkit/runtime-client-gql";

interface CrewsFeedback extends CrewsStateItem {
  /**
   * Output of the task execution
   */
  task_output?: string;
}

/**
 * Renders your Crew's steps & tasks in real-time.
 */
function CrewStateRenderer({
  state,
  status,
}: {
  state: CrewsAgentState;
  status: CrewsResponseStatus;
}) {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const contentRef = useRef<HTMLDivElement>(null);
  const prevItemsLengthRef = useRef<number>(0);
  const [highlightId, setHighlightId] = useState<string | null>(null);

  // Combine steps + tasks
  const items = useMemo(() => {
    if (!state) return [];
    return [...(state.steps || []), ...(state.tasks || [])].sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }, [state]);

  // Highlight newly added item & auto-scroll
  useEffect(() => {
    if (!state) return;
    if (items.length > prevItemsLengthRef.current) {
      const newestItem = items[items.length - 1];
      setHighlightId(newestItem.id);
      setTimeout(() => setHighlightId(null), 1500);

      if (contentRef.current && !isCollapsed) {
        contentRef.current.scrollTop = contentRef.current.scrollHeight;
      }
    }
    prevItemsLengthRef.current = items.length;
  }, [items, isCollapsed, state]);

  if (!state) {
    return <div>Loading crew state...</div>;
  }

  // Hide entirely if collapsed & empty & not in progress
  if (isCollapsed && items.length === 0 && status !== "inProgress") return null;

  return (
    <div style={{ marginTop: "8px", fontSize: "0.9rem" }}>
      <div
        style={{ cursor: "pointer", display: "flex", alignItems: "center" }}
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <span style={{ marginRight: 4 }}>{isCollapsed ? "\u25B6" : "\u25BC"}</span>
        {status === "inProgress" ? "Crew is analyzing..." : "Crew analysis"}
      </div>

      {!isCollapsed && (
        <div
          ref={contentRef}
          style={{
            maxHeight: "200px",
            overflow: "auto",
            borderLeft: "1px solid #ccc",
            paddingLeft: "8px",
            marginLeft: "4px",
            marginTop: "4px",
          }}
        >
          {items.length > 0 ? (
            items.map((item) => {
              const isTool = (item as CrewsToolStateItem).tool !== undefined;
              const isHighlighted = item.id === highlightId;
              return (
                <div
                  key={item.id}
                  style={{
                    marginBottom: "8px",
                    animation: isHighlighted ? "fadeIn 0.5s" : undefined,
                  }}
                >
                  <div style={{ fontWeight: "bold" }}>
                    {isTool
                      ? (item as CrewsToolStateItem).tool
                      : (item as CrewsTaskStateItem).name}
                  </div>
                  {"thought" in item && item.thought && (
                    <div style={{ opacity: 0.8, marginTop: "4px" }}>
                      Thought: {item.thought}
                    </div>
                  )}
                  {"result" in item && item.result !== undefined && (
                    <pre style={{ fontSize: "0.85rem", marginTop: "4px" }}>
                      {JSON.stringify(item.result, null, 2)}
                    </pre>
                  )}
                  {"description" in item && item.description && (
                    <div style={{ marginTop: "4px" }}>{item.description}</div>
                  )}
                </div>
              );
            })
          ) : (
            <div style={{ opacity: 0.7 }}>No activity yet...</div>
          )}
        </div>
      )}

      {/* Simple fadeIn animation */}
      <style>{`
        @keyframes fadeIn {
          0% { opacity: 0; transform: translateY(4px); }
          100% { opacity: 1; transform: translateY(0); }
        }
      `}</style>
    </div>
  );
}

/**
 * Renders a simple UI for agent-requested user feedback (Approve / Reject).
 */
function CrewHumanFeedbackRenderer({
  feedback,
  respond,
  status,
}: {
  feedback: CrewsFeedback;
  respond?: (input: string) => void;
  status: CrewsResponseStatus;
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [userResponse, setUserResponse] = useState<string | null>(null);

  if (status === "complete") {
    return (
      <div style={{ marginTop: 8, textAlign: "right" }}>
        {userResponse || "Feedback submitted."}
      </div>
    );
  }

  if (status === "inProgress" || status === "executing") {
    return (
      <div style={{ marginTop: 8 }}>
        {isExpanded && (
          <div
            style={{
              border: "1px solid #ddd",
              padding: "8px",
              marginBottom: "8px",
            }}
          >
            {feedback.task_output}
          </div>
        )}
        <div style={{ textAlign: "right" }}>
          <button
            style={{ marginRight: 8 }}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Hide" : "Show"} Feedback
          </button>
          <button
            style={{
              marginRight: 8,
              backgroundColor: "#222222",
              border: "none",
              padding: "8px 16px",
              color: "white",
              cursor: "pointer",
              borderRadius: "4px",
            }}
            onClick={() => {
              setUserResponse("Approved");
              /**
               * This string is arbitrary. It can be any serializable input that will be forwarded to your Crew as feedback.
               */
              respond?.("Approve");
            }}
          >
            Approve
          </button>
          <button
            style={{
              backgroundColor: "#222222",
              border: "none",
              padding: "8px 16px",
              color: "white",
              cursor: "pointer",
              borderRadius: "4px",
            }}
            onClick={() => {
              setUserResponse("Rejected");
              /**
               * This string is arbitrary. It can be any serializable input that will be forwarded to your Crew as feedback.
               */
              respond?.("Reject");
            }}
          >
            Reject
          </button>
        </div>
      </div>
    );
  }

  return null;
}

/**
 * useCrewQuickstart
 * Minimal example that:
 * 1) Sets up a crew/agent
 * 2) Handles text-based user input (get_input)
 * 3) Renders real-time crew state
 * 4) Handles "crew_requesting_feedback"
 */
export const useCrewQuickstart = ({
  crewName,
  inputs,
}: {
  crewName: string;
  inputs: Array<string>;
}): {
  output: string;
} => {
  const [initialMessageSent, setInitialMessageSent] = useState(false);

  const { state, setState, run } = useCoAgent<
    CrewsAgentState & {
      result: string;
      inputs: Record<string, string>;
    }
  >({
    name: crewName,
    initialState: {
      inputs: {},
      result: "Crew result will appear here...",
    },
  });

  const { appendMessage, isLoading } = useCopilotChat();

  const instructions =
    "INPUTS ARE ABSOLUTELY REQUIRED. Please call getInputs before proceeding with anything else.";

  // Render an initial message when the chat is first loaded
  useEffect(() => {
    if (initialMessageSent || isLoading) return;

    setTimeout(async () => {
      await appendMessage(
        new TextMessage({
          content: "Hi, Please provide your inputs before we get started.",
          role: MessageRole.Developer,
        })
      );
      setInitialMessageSent(true);
    }, 0);
  }, []);

  useEffect(() => {
    if (!initialMessageSent && Object.values(state?.inputs || {}).length > 0) {
      appendMessage(
        new TextMessage({
          role: MessageRole.Developer,
          content: "My inputs are: " + JSON.stringify(state?.inputs),
        })
      ).then(() => {
        setInitialMessageSent(true);
      });
    }
  }, [initialMessageSent, state?.inputs]);

  useCopilotAdditionalInstructions({
    instructions,
    available:
      Object.values(state?.inputs || {}).length > 0 ? "enabled" : "disabled",
  });

  useCopilotAction({
    name: "getInputs",
    followUp: false,
    description:
      "This action allows Crew to get required inputs from the user before starting the Crew.",
    renderAndWaitForResponse({ status }) {
      if (status === "inProgress" || status === "executing") {
        return (
          <form
            style={{ display: "flex", flexDirection: "column", gap: "16px" }}
            onSubmit={async (e: React.FormEvent<HTMLFormElement>) => {
              e.preventDefault();
              const form = e.currentTarget;
              const input = form.elements.namedItem(
                "input"
              ) as HTMLTextAreaElement;
              const inputValue = input.value;
              const inputKey = input.id;

              setState({
                ...state,
                inputs: {
                  ...state.inputs,
                  [inputKey]: inputValue,
                },
```

----------------------------------------

TITLE: Implementing useCopilotAction Hook for User Feedback in React JSX
DESCRIPTION: This code defines a copilot action for requesting feedback from users. It renders a CrewHumanFeedbackRenderer component when the AI system needs human input and handles the response flow.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/snippets/crew-quickstart.mdx#2025-04-23_snippet_2

LANGUAGE: JSX
CODE:
```
useCopilotAction({
    name: "crew_requesting_feedback",
    description: "Request feedback from the user",
    renderAndWaitForResponse(props) {
      const { status, args, respond } = props;
      return (
        <CrewHumanFeedbackRenderer
          feedback={args as unknown as CrewsFeedback}
          respond={respond}
          status={status as CrewsResponseStatus}
        />
      );
    },
  });
```

----------------------------------------

TITLE: Rendering Agent State in React Chat Component
DESCRIPTION: This snippet shows how to use the useCoAgentStateRender hook to render the state of an agent within a React chat component, displaying search progress.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/generative-ui/agentic.mdx#2025-04-23_snippet_4

LANGUAGE: tsx
CODE:
```
import { useCoAgentStateRender } from "@copilotkit/react-core";

type AgentState = {
  searches: {
    query: string;
    done: boolean;
  }[];
};

function YourMainContent() {
  useCoAgentStateRender<AgentState>({
    name: "sample_agent", // the name the agent is served as
    render: ({ state }) => (
      <div>
        {state.searches?.map((search, index) => (
          <div key={index}>
            {search.done ? "✅" : "❌"} {search.query}{search.done ? "" : "..."}
          </div>
        ))}
      </div>
    ),
  });

  return <div>...</div>;
}
```

----------------------------------------

TITLE: Using useCoAgent Hook in React
DESCRIPTION: This TypeScript snippet demonstrates how to use the useCoAgent hook to manage agent state in a React component. It includes state initialization, reading, and updating.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/shared-state/in-app-agent-write.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

// Define the agent state type, should match the actual state of your agent
type AgentState = {
  language: "english" | "spanish";
}

// Example usage in a pseudo React component
function YourMainContent() {
  const { state, setState } = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: { language: "spanish" }  // optionally provide an initial state
  });

  // ...

  const toggleLanguage = () => {
    setState({ language: state.language === "english" ? "spanish" : "english" });
  };

  // ...

  return (
    // style excluded for brevity
    <div>
      <h1>Your main content</h1>
      <p>Language: {state.language}</p>
      <button onClick={toggleLanguage}>Toggle Language</button>
    </div>
  );
}
```

----------------------------------------

TITLE: Using useCoAgent Hook in React Component
DESCRIPTION: Demonstrates how to use the useCoAgent hook to manage agent state in a React component, including toggling language and rendering state.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/shared-state/in-app-agent-write.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  language: "english" | "spanish";
}

function YourMainContent() {
  const { state, setState } = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: { language: "spanish" }
  });

  const toggleLanguage = () => {
    setState({ language: state.language === "english" ? "spanish" : "english" });
  };

  return (
    <div>
      <h1>Your main content</h1>
      <p>Language: {state.language}</p>
      <button onClick={toggleLanguage}>Toggle Language</button>
    </div>
  );
}
```

----------------------------------------

TITLE: Using useCoAgent Hook to Display Agent State in React
DESCRIPTION: This snippet demonstrates how to use the useCoAgent hook from CopilotKit to retrieve and display the state of a connected agent in a React component. It shows how to initialize the hook with an agent name and initial state, and then render the agent's output.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-crews/shared-state/in-app-agent-read.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

function YourMainContent() {
  const { state } = useCoAgent({
    name: "research_crew",
    initialState: {
      inputs: {
        topic: "",
        current_year: "2025",
      },
      outputs: "Report will appear here",
    },
  });

  // ...

  return (
    // style excluded for brevity
    <div>
      <h1>Your report:</h1>
      <p>{state.outputs}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Configuring useCoAgentStateRender Hook in React JSX
DESCRIPTION: This code configures the useCoAgentStateRender hook to display the current state and status of a crew agent using a CrewStateRenderer component. It connects the crew's state management with the UI rendering system.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/snippets/crew-quickstart.mdx#2025-04-23_snippet_1

LANGUAGE: JSX
CODE:
```
useCoAgentStateRender({
    name: crewName,
    render: ({ state, status }) => (
      <CrewStateRenderer state={state} status={status} />
    ),
  });
```

----------------------------------------

TITLE: Using useCoAgent Hook for CrewAI State Management in React TSX
DESCRIPTION: Example implementation showing how to use the useCoAgent hook to manage CrewAI agent state in a React component. Demonstrates state initialization, type definition, and state updates through a form input.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-crews/shared-state/in-app-agent-write.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

// Define the agent state type, should match the actual state of your agent
type AgentState = {
  language: "english" | "spanish";
}

// Example usage in a pseudo React component
function YourMainContent() {
  const { state, setState } = useCoAgent({
    name: "research_crew",
    initialState: {
      inputs: {
        topic: "",
        current_year: "2025",
      },
      outputs: "Report will appear here",
    },
  });
  // ...

  return (
    // style excluded for brevity
    <div>
      <label htmlFor="topic">
        Topic
      </label>
      <input
        type="text"
        value={state.inputs.topic}
        onChange={(e) =>
          setState({
            ...state,
            inputs: { ...state.inputs, topic: e.target.value },
          })
        }
      />
    </div>
  );
}
```

----------------------------------------

TITLE: Rendering Agent State Outside Chat UI (React/TSX)
DESCRIPTION: This React/TSX example illustrates how to fetch and render the agent's state anywhere in the application, outside of the main chat component, using the `useCoAgent` hook. It retrieves the `state` object for a named agent and then maps the `searches` array to display its progress, providing flexible UI integration.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/generative-ui/agentic.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core"; // [!code highlight]
// ...

// Define the state of the agent, should match the state of the agent in your Flow.
type AgentState = {
  searches: {
    query: string;
    done: boolean;
  }[];
};

function YourMainContent() {
  // ...

  // [!code highlight:5]
  const { state } = useCoAgent<AgentState>({
    name: "sample_agent", // the name the agent is served as
  })

  // ...

  return (
    <div>
      {/* ... */}
      <div className="flex flex-col gap-2 mt-4">
        // [!code highlight:6]
        {state.searches?.map((search, index) => (
          <div key={index} className="flex flex-row">
            {search.done ? "✅" : "❌"} {search.query}
          </div>
        ))}
      </div>
    </div>
  )
}
```

----------------------------------------

TITLE: Using useCoAgent Hook in React
DESCRIPTION: Implementation of the useCoAgent hook to read and display agent state in a React component.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/shared-state/in-app-agent-read.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  language: "english" | "spanish";
}

function YourMainContent() {
  const { state } = useCoAgent<AgentState>({
    name: "sample_agent",
    initialState: { language: "spanish" }  // optionally provide an initial state
  });

  return (
    <div>
      <h1>Your main content</h1>
      <p>Language: {state.language}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Configuring CopilotRuntime with LangGraph Endpoint
DESCRIPTION: Setting up the CopilotRuntime with langGraphPlatformEndpoint configuration for self-hosted deployment.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-4-integrate-the-agent.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
const runtime = new CopilotRuntime({
    remoteEndpoints: [
      langGraphPlatformEndpoint({
        deploymentUrl: "http://localhost:<port_number>",
        langsmithApiKey: "your-langsmith-api-key",
        agents: [{ 
          name: 'travel', 
          description: 'A travel assistant that can help with planning trips.' 
        }]
      }),
    ],
});
```

----------------------------------------

TITLE: Implementing Frontend Paired Action with React
DESCRIPTION: Creates a frontend component that pairs with the backend fetchUser action using useCopilotAction hook. Handles the display of user data returned from the backend operation.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/guides/front-backend-action-pairing.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
"use client" // only necessary if you are using Next.js with the App Router.
import { useCopilotAction } from "@copilotkit/react-core";

export function MyComponent() {
const [userName, setUserName] = useState<string>('stranger');

// Define Copilot action
useCopilotAction({
    name: "displayUser",
    description: "Display the user name fetched from the backend",
    pairedAction: "fetchUser",
    available: "frontend",
    parameters: [
    {
        name: "name",
        type: "string",
        description: "The user name",
    },
    ],
    handler: async ({ name }) => {
        setUserName(name);
    },
});

return (
    <h1>
        hello {userName}
    </ul>
);
}
```

----------------------------------------

TITLE: Locking Agent in CopilotKit Provider
DESCRIPTION: Configuring the CopilotKit provider to lock all requests to a specific agent.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-4-integrate-the-agent.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
<CopilotKit
  // ...
  agent="travel"
>
  {...}
</CopilotKit>
```

----------------------------------------

TITLE: Using useCoAgentStateRender Hook in React
DESCRIPTION: Implementation of the useCoAgentStateRender hook to render agent state updates in the chat UI.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/shared-state/in-app-agent-read.mdx#2025-04-23_snippet_3

LANGUAGE: tsx
CODE:
```
import { useCoAgentStateRender } from "@copilotkit/react-core";

type AgentState = {
  language: "english" | "spanish";
}

function YourMainContent() {
  useCoAgentStateRender({
    name: "sample_agent",
    render: ({ state }) => {
      if (!state.language) return null;
      return <div>Language: {state.language}</div>;
    },
  });
}
```

----------------------------------------

TITLE: Implementing Agent State Rendering with useCoAgentStateRender in React
DESCRIPTION: Example showing how to use the useCoAgentStateRender hook to render UI components based on an agent's state. The hook accepts a name, optional nodeName, and a render function that receives status, state, and nodeName as props.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/reference/hooks/useCoAgentStateRender.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCoAgentStateRender } from "@copilotkit/react-core";

type YourAgentState = {
  agent_state_property: string;
}

useCoAgentStateRender<YourAgentState>({
  name: "basic_agent",
  nodeName: "optionally_specify_a_specific_node",
  render: ({ status, state, nodeName }) => {
    return (
      <YourComponent
        agentStateProperty={state.agent_state_property}
        status={status}
        nodeName={nodeName}
      />
    );
  },
});
```

----------------------------------------

TITLE: Integrating CrewAI Quickstart Component in Next.js
DESCRIPTION: This TypeScript React snippet demonstrates how to integrate the `useCrewQuickstart` hook into a Next.js page. It imports the hook and calls it with the crew name and required inputs, enabling CopilotKit functionality within the application and allowing interaction with the CrewAI agent.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-crews/quickstart.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
"use client";
import React from "react";
import useCrewQuickstart from "./use-crew-quickstart";

export default function YourApp() {
useCrewQuickstart({
    crewName: "<REPLACE_WITH_YOUR_CREW_NAME>",
    /**
     * List of input required to start your crew (location e.g)
    */
    inputs: ["location"]
})
return (
    <>
    {/* Existing markup */}
    </>
);
}
```

----------------------------------------

TITLE: Initializing Agent with Config in React/TypeScript
DESCRIPTION: This snippet demonstrates how to initialize a LangGraph agent within a React component using the `useCoAgent` hook. It shows how to pass execution parameters, such as an `authToken`, by providing a `config` object with a nested `configurable` property. It also sets a `recursion_limit`.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/advanced/adding-runtime-configuration.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

function YourMainContent() {
  // ...

  useCoAgent<AgentState>({
    name: "sample_agent",
    config: {
      configurable: {
        authToken: 'example-token'
      },
      recursion_limit: 50,
    }
  })

  // ...

  return (... your component UI markdown)
}
```

----------------------------------------

TITLE: Installing CopilotKit Dependencies with NPM
DESCRIPTION: Command to install the core CopilotKit React libraries needed for implementing AI capabilities in a web application. This installs both the core functionality and UI components.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/tutorials/ai-todo-app/step-2-setup-copilotkit.mdx#2025-04-23_snippet_0

LANGUAGE: package-install
CODE:
```
npm install @copilotkit/react-core @copilotkit/react-ui
```

----------------------------------------

TITLE: Rendering Emitted State in React Component
DESCRIPTION: Implementation of the useCoAgentStateRender hook in a React component to receive and render the manually emitted state from the agent. This allows for real-time display of search progress in the UI.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-5-stream-progress.mdx#2025-04-23_snippet_3

LANGUAGE: typescript
CODE:
```
import { useCoAgent, useCoAgentStateRender } from "@copilotkit/react-core";
import { SearchProgress } from "@/components/SearchProgress";

export const TripsProvider = ({ children }: { children: ReactNode }) => {
  // ...
  
  const { state, setState } = useCoAgent<AgentState>({
    name: "travel",
    initialState: {
      trips: defaultTrips,
      selected_trip_id: defaultTrips[0].id,
    },
  });

  useCoAgentStateRender<AgentState>({
    name: "travel",
    render: ({ state }) => {
      if (state.search_progress) {
        return <SearchProgress progress={state.search_progress} />
      }
      return null;
    },
  });

  // ...
}
```

----------------------------------------

TITLE: Installing Agent Dependencies with Poetry
DESCRIPTION: Commands to navigate to the agent directory and install dependencies using Poetry.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/examples/coagents-ai-researcher/README.md#2025-04-23_snippet_0

LANGUAGE: sh
CODE:
```
cd agent
poetry install
```

----------------------------------------

TITLE: Interactive Counter Implementation with useCoAgent
DESCRIPTION: Demonstrates practical usage of useCoAgent hook in a React component with interactive state management.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/reference/hooks/useCoAgent.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
const { state, setState } = useCoAgent<AgentState>({
  name: "my-agent",
  initialState: {
    count: 0,
  },
});

return (
  <div>
    <p>Count: {state.count}</p>
    <button onClick={() => setState({ count: state.count + 1 })}>Increment</button>
  </div>
);
```

----------------------------------------

TITLE: Creating Tunnel with CopilotKit CLI
DESCRIPTION: Command to create a tunnel between locally running LangGraph agent and Copilot Cloud.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-4-integrate-the-agent.mdx#2025-04-23_snippet_0

LANGUAGE: bash
CODE:
```
npx copilotkit@latest dev --port <port_number>
```

----------------------------------------

TITLE: Observing Agent State Predictions in React (TSX)
DESCRIPTION: This TSX snippet demonstrates how to observe and render both predicted and final agent states in a React component using CopilotKit's `useCoAgent` and `useCoAgentStateRender` hooks. It displays intermediate progress updates as well as the final list of observed steps.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/shared-state/predictive-state-updates.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
import { useCoAgent, useCoAgentStateRender } from '@copilotkit/react-core';

// ...

const YourMainContent = () => {
    // Get access to both predicted and final states
    const { state } = useCoAgent({ name: "sample_agent" });

    // Add a state renderer to observe predictions
    useCoAgentStateRender({
        name: "sample_agent",
        render: ({ state }) => {
            if (!state.observed_steps?.length) return null;
            return (
                <div>
                    <h3>Current Progress:</h3>
                    <ul>
                        {state.observed_steps.map((step, i) => (
                            <li key={i}>{step}</li>
                        ))}
                    </ul>
                </div>
            );
        }
    });

    return (
        <div>
            <h1>Agent Progress</h1>
            {state.observed_steps?.length > 0 && (
                <div>
                    <h3>Final Steps:</h3>
                    <ul>
                        {state.observed_steps.map((step, i) => (
                            <li key={i}>{step}</li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
}
```

----------------------------------------

TITLE: Importing React Components and Icons
DESCRIPTION: Import statements for required React components including YouTube video player, icons, and UI elements.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/index.mdx#2025-04-23_snippet_0

LANGUAGE: jsx
CODE:
```
import { YouTubeVideo } from "@/components/react/youtube-video";
import { FaGithub } from "react-icons/fa";
import { PiMonitor } from "react-icons/pi";
import { Button } from "@/components/ui/button";
import Link from "next/link";
```

----------------------------------------

TITLE: Implementing CoAgents with LangGraph Integration in TypeScript
DESCRIPTION: Shows how to implement in-app LangGraph Agents using CopilotKit, including state sharing, generative UI, human-in-the-loop approval flows, and intermediate state streaming with both LangGraph.js and Python.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/README.md#2025-04-23_snippet_1

LANGUAGE: typescript
CODE:
```
// Share state between app and agent
const { agentState } = useCoAgent({ 
  name: "basic_agent", 
  initialState: { input: "NYC" } 
});

// agentic generative UI
useCoAgentStateRender({
  name: "basic_agent",
  render: ({ state }) => <WeatherDisplay {...state.final_response} />,
});

// Human in the Loop (Approval)
useCopilotAction({
    name: "email_tool",
    parameters: [{ name: "email_draft", type: "string", description: "The email content", required: true }],
    renderAndWaitForResponse: ({ args, status, respond }) => (
      <EmailConfirmation
        emailContent={args.email_draft || ""}
        isExecuting={status === "executing"}
        onCancel={() => respond?.({ approved: false })}
        onSend={() => respond?.({ approved: true, metadata: { sentAt: new Date().toISOString() } })}
      />
    ),
  });

// ---

// intermediate agent state streaming (supports both LangGraph.js + LangGraph python)
const modifiedConfig = copilotKitCustomizeConfig(config, {
  emitIntermediateState: [{ 
    stateKey: "outline", 
    tool: "set_outline", 
    toolArgument: "outline" 
  }],
});
const response = await ChatOpenAI({ model: "gpt-4o" }).invoke(messages, modifiedConfig);
```

----------------------------------------

TITLE: Example Agent Query: React Opinion
DESCRIPTION: An example query to test the integrated LangGraph agent, demonstrating how to interact with it by asking for its opinion on React.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/quickstart.mdx#_snippet_4

LANGUAGE: plaintext
CODE:
```
What do you think about React?
```

----------------------------------------

TITLE: Implementing Research Context with useCoAgent Hook
DESCRIPTION: Implements a React context provider using CopilotKit's useCoAgent hook to manage research state and provide bidirectional state synchronization between the frontend and LangGraph agent.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/agent-native-app/step-6-shared-state.mdx#2025-04-23_snippet_1

LANGUAGE: typescript
CODE:
```
interface ResearchContextType {
    state: ResearchState;
    setResearchState: (newState: ResearchState | ((prevState: ResearchState) => ResearchState)) => void
    sourcesModalOpen: boolean
    setSourcesModalOpen: (open: boolean) => void
    runAgent: () => void
}

const ResearchContext = createContext<ResearchContextType | undefined>(undefined)

export function ResearchProvider({ children }: { children: ReactNode }) {
    const [sourcesModalOpen, setSourcesModalOpen] = useState<boolean>(false)
    const { state, setState, run } = useCoAgent<ResearchState>({
        name: 'agent',
        initialState: {},
    });

    return (
        <ResearchContext.Provider 
            value={{ 
              state, 
              setResearchState: setState as ResearchContextType['setResearchState'], 
              setSourcesModalOpen, 
              sourcesModalOpen, 
              runAgent: run
            }}>
            {children}
        </ResearchContext.Provider>
    )
}
```

----------------------------------------

TITLE: Rendering Backend Actions in React Frontend
DESCRIPTION: Example of how to render backend actions safely in the frontend using useCopilotAction hook.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/examples/copilot-chat-with-your-data/README.md#2025-04-23_snippet_5

LANGUAGE: tsx
CODE:
```
useCopilotAction({
  name: "searchInternet",
  available: "disabled",
  description: "Searches the internet for information.",
  parameters: [
    {
      name: "query",
      type: "string",
      description: "The query to search the internet for.",
      required: true,
    }
  ],
  render: ({args, status}) => {
    return <SearchResults query={args.query || 'No query provided'} status={status} />;
  }
});
```

----------------------------------------

TITLE: Basic useCoAgent Hook Implementation in TypeScript React
DESCRIPTION: Demonstrates the basic implementation of useCoAgent hook with a simple counter state example.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/reference/hooks/useCoAgent.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCoAgent } from "@copilotkit/react-core";

type AgentState = {
  count: number;
}

const agent = useCoAgent<AgentState>({
  name: "my-agent",
  initialState: {
    count: 0,
  },
});
```

----------------------------------------

TITLE: Adding Human-in-the-Loop UI with useCopilotAction in React
DESCRIPTION: This snippet demonstrates how to use the `useCopilotAction` hook from `@copilotkit/react-core` to create a UI component that requests feedback from the user on a CrewAI agent's output. It displays the agent's arguments and provides interactive buttons ('Cancel', 'Approve Kickoff') to allow the user to respond, enabling Human-in-the-Loop (HITL) control over the agent's execution flow. This component requires a running CrewAI agent connected to CopilotKit Cloud to function end-to-end.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-crews/human-in-the-loop/flow.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCopilotAction } from "@copilotkit/react-core";
import { Markdown } from "@copilotkit/react-ui";

function YourMainContent() {
  useCopilotAction({
    name: "crew_requesting_feedback",
    description: "Request feedback from the user on the crew's output",
    renderAndWaitForResponse: ({ args, respond, status }) => (
      <div>
        <pre>{args}</pre>
        <div className={`flex gap-4 pt-4 ${status !== "executing" ? "hidden" : ""}`}>
          <button
            onClick={() => respond?.("Reject")}
            disabled={status !== "executing"}
            className="border p-2 rounded-xl w-full"
          >
            Cancel
          </button>
          <button
            onClick={() => respond?.("Approve")}
            disabled={status !== "executing"}
            className="bg-blue-500 text-white p-2 rounded-xl w-full"
          >
            Approve Kickoff
          </button>
        </div>
      </div>
    )
  });
}
```

----------------------------------------

TITLE: Example Agent Query: AI Understanding
DESCRIPTION: An example query to test the integrated LangGraph agent, demonstrating how to interact with it by asking for help understanding AI.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/quickstart.mdx#_snippet_3

LANGUAGE: plaintext
CODE:
```
Can you help me understand AI?
```

----------------------------------------

TITLE: Importing React Components and Icons for Agentic Copilots UI
DESCRIPTION: Import statements for React components including TailoredContent, various icons from react-icons and lucide-react packages used in the agentic copilots documentation interface.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/concepts/agentic-copilots.mdx#2025-04-23_snippet_0

LANGUAGE: jsx
CODE:
```
import {
  TailoredContent,
  TailoredContentOption,
} from "@/components/react/tailored-content";
import { BsFillCloudHaze2Fill as CloudIcon } from "react-icons/bs";
import { FaServer as SelfHostIcon } from "react-icons/fa6";
import { SiLangchain } from "react-icons/si";
import { LinkIcon } from "lucide-react";
import {
  RocketIcon,
  GraduationCapIcon,
  CodeIcon,
  VideoIcon,
} from "lucide-react";
```

----------------------------------------

TITLE: Basic Usage of useCopilotAdditionalInstructions Hook in React
DESCRIPTION: Demonstrates the simplest way to use the useCopilotAdditionalInstructions hook to provide additional instructions to the Copilot agent.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/reference/hooks/useCopilotAdditionalInstructions.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
import { useCopilotAdditionalInstructions } from "@copilotkit/react-core";

export function MyComponent() {
  useCopilotAdditionalInstructions({
    instructions: "Do not answer questions about the weather.",
  });
}
```

----------------------------------------

TITLE: Defining and Compiling StateGraph for AI Agent in TypeScript
DESCRIPTION: This code defines the state graph for the AI agent, adding nodes for chat, feedback collection, and feedback handling. It also sets up the edges between these nodes and compiles the graph with specific configurations.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/human-in-the-loop/node-flow.mdx#2025-04-23_snippet_6

LANGUAGE: TypeScript
CODE:
```
export const graph = new StateGraph(AgentStateAnnotation)
  .addNode("chatNode", chatNode, { ends: ["getFeedback"] })
  .addNode("getFeedback", getFeedback)
  .addNode("handleFeedback", handleFeedback)
  .addEdge("__start__", "chatNode")
  .addEdge("getFeedback", "handleFeedback")
  .addEdge("handleFeedback", "chatNode")
  .compile({
    checkpointer: new MemorySaver(),
    interruptAfter: ["getFeedback"],
  });
```

----------------------------------------

TITLE: Using useCopilotAdditionalInstructions for Stage-Specific Tool Guidance in TSX
DESCRIPTION: This TSX snippet demonstrates the `useStageGetPaymentInfo` custom React hook. It utilizes the `useCopilotAdditionalInstructions` hook from CopilotKit to conditionally provide specific instructions to the AI agent only when the application's `stage` variable (from `useGlobalState`) is 'getPaymentInfo'. These instructions explicitly direct the AI to announce the current task and then call the 'getPaymentInformation' action, ensuring the correct tool is used at this specific stage. The hook's execution is dependent on changes to the `stage` variable.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/cookbook/state-machine.mdx#2025-04-23_snippet_10

LANGUAGE: tsx
CODE:
```
export function useStageGetPaymentInfo() {
  const { setCardInfo, stage, setStage } = useGlobalState();

  // Conditionally add additional instructions for the agent's prompt.
  useCopilotAdditionalInstructions({
    available: stage === "getPaymentInfo" ? "enabled" : "disabled",
    // [!code highlight:6]
    instructions: `
        CURRENT STATE: You are now getting the payment information of the user. 
        Say, 'Great! Now I need to get your payment information.' and MAKE SURE 
        to then call the 'getPaymentInformation' action.
    `,
  }, [stage]);

  // ...

}
```

----------------------------------------

TITLE: Configuring Environment Variables for Agent
DESCRIPTION: Content for the .env file in the agent directory, specifying API keys for OpenAI and Tavily.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/examples/coagents-ai-researcher/README.md#2025-04-23_snippet_1

LANGUAGE: plaintext
CODE:
```
OPENAI_API_KEY=...
TAVILY_API_KEY=...
```

----------------------------------------

TITLE: Integrating CopilotKit Provider in React Application
DESCRIPTION: Code snippet showing how to wrap a React application with the CopilotKit provider. This setup is crucial for enabling CopilotKit functionality throughout the application.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/agent-native-app/step-3-setup-copilotkit.mdx#2025-04-23_snippet_3

LANGUAGE: tsx
CODE:
```
"use client";

// ...
import { CopilotKit } from "@copilotkit/react-core";
import "@copilotkit/react-ui/styles.css";
// ...

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
      <html lang="en" className="h-full">
        <body className={`${lato.variable} ${noto.className} antialiased h-full`}>
          <CopilotKit
            publicApiKey={process.env.NEXT_PUBLIC_CPK_PUBLIC_API_KEY}
          >
            <TooltipProvider>
              <ResearchProvider>
                {children}
              </ResearchProvider>
            </TooltipProvider>
          </CopilotKit>
        </body>
      </html>
    );
}
```

----------------------------------------

TITLE: Conditional Instructions with useCopilotAdditionalInstructions in React
DESCRIPTION: Shows how to conditionally add instructions to an AI assistant based on component state using the useCopilotAdditionalInstructions hook.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/guides/custom-ai-assistant-behavior.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
function Chat() {
  const [showWeather, setShowWeather] = useState(false);

  useCopilotAdditionalInstructions({
    instructions: "Do not answer questions about the weather.",
    available: showWeather ? "enabled" : "disabled"
  }, showWeather);
}
```

----------------------------------------

TITLE: Setting Instructions Programmatically with useCopilotContext
DESCRIPTION: Shows how to programmatically set chat instructions using the useCopilotContext hook in a React functional component.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/(root)/guides/custom-ai-assistant-behavior.mdx#2025-04-23_snippet_3

LANGUAGE: tsx
CODE:
```
import { useEffect } from 'react';
import { useCopilotContext } from "@copilotkit/react-core";

const Home: React.FC = () => {
  // [!code highlight:6]
  const { setChatInstructions } = useCopilotContext();

  useEffect(() => {
    setChatInstructions("You are assisting the user as best as you can. Answer in the best way possible given the data you have.");
  }, [setChatInstructions]);

  return <>{/* Your components */}</>;
};
```

----------------------------------------

TITLE: Setting Up CrewAI Agent with CopilotKit for HITL
DESCRIPTION: This Python snippet illustrates the setup of a CrewAI agent for Human-in-the-Loop (HITL) interactions. The `AgentState` inherits from `CopilotKitState` to integrate CopilotKit actions. The `check_for_user_feedback` method processes user responses ('SEND' or 'CANCEL') from the frontend, updating the agent's messages. The `chat` method uses `copilotkit_stream` with `litellm.completion` to interact with the LLM, binding CopilotKit actions as tools and streaming responses back to the agent's state.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/crewai-flows/human-in-the-loop/flow.mdx#_snippet_1

LANGUAGE: python
CODE:
```
from typing import Any, cast
from crewai.flow.flow import Flow, start, listen
from copilotkit import CopilotKitState
from copilotkit.crewai import copilotkit_stream
from litellm import completion


class AgentState(CopilotKitState):
    pass


class SampleAgentFlow(Flow[AgentState]):

    @start()
    async def check_for_user_feedback(self):
        if not self.state.get("messages"):
            return

        last_message = cast(Any, self.state["messages"][-1])

        # Expecting the result of a CopilotKit tool call (SEND/CANCEL)
        if last_message["role"] == "tool":
            user_response = last_message.get("content")

            if user_response == "SEND":
                self.state["messages"].append({
                    "role": "assistant",
                    "content": "✅ Great! Sending your essay via email.",
                })
                return

            if user_response == "CANCEL":
                self.state["messages"].append({
                    "role": "assistant",
                    "content": "❌ Okay, we can improve the draft. What would you like to change?",
                })
                return

        # If no tool result yet, or it's a user message, prompt next step
        if last_message.get("role") == "user":
            self.state["messages"].append({
                "role": "system",
                "content": (
                    "You write essays. Use your tools to write an essay; "
                    "don’t just write it in plain text."
                )
            })

    @listen(check_for_user_feedback)
    async def chat(self):
        messages = self.state.get("messages", [])

        system_message = {
            "role": "system",
            "content": (
                "You write essays. Use your tools to write an essay; "
                "don’t just write it in plain text."
            )
        }

        response = await copilotkit_stream(
            completion(
                model="openai/gpt-4o",
                messages=[system_message, *messages],
                tools=self.state["copilotkit"]["actions"],
                stream=True
            )
        )

        self.state["messages"].append(response.choices[0].message)

```

----------------------------------------

TITLE: Environment Variables Configuration
DESCRIPTION: Template for setting up OpenAI and Google Maps API keys in the .env file
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/ai-travel-app/step-2-langgraph-agent.mdx#2025-04-23_snippet_2

LANGUAGE: txt
CODE:
```
OPENAI_API_KEY=<your-openai-api-key>
GOOGLE_MAPS_API_KEY=<your-google-maps-api-key>
```

----------------------------------------

TITLE: Implementing Core CopilotKit Features in TypeScript
DESCRIPTION: Demonstrates the main features of CopilotKit including headless UI, pre-built components, frontend RAG, knowledge base integration, actions, and structured autocompletion. Shows how to create customizable AI assistants with various integration options.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/README.md#2025-04-23_snippet_0

LANGUAGE: typescript
CODE:
```
// Headless UI with full control
const { visibleMessages, appendMessage, setMessages, ... } = useCopilotChat();

// Pre-built components with deep customization options (CSS + pass custom sub-components)
<CopilotPopup 
  instructions={"You are assisting the user as best as you can. Answer in the best way possible given the data you have."} 
  labels={{ title: "Popup Assistant", initial: "Need any help?" }} 
/>

// ---

// Frontend RAG
useCopilotReadable({
  description: "The current user's colleagues",
  value: colleagues,
});

// knowledge-base integration
useCopilotKnowledgebase(myCustomKnowledgeBase)

// ---

// Frontend actions + generative UI, with full streaming support
useCopilotAction({
  name: "appendToSpreadsheet",
  description: "Append rows to the current spreadsheet",
  parameters: [
    { name: "rows", type: "object[]", attributes: [{ name: "cells", type: "object[]", attributes: [{ name: "value", type: "string" }] }] }
  ],
  render: ({ status, args }) => <Spreadsheet data={canonicalSpreadsheetData(args.rows)} />,
  handler: ({ rows }) => setSpreadsheet({ ...spreadsheet, rows: [...spreadsheet.rows, ...canonicalSpreadsheetData(rows)] }),
});

// ---

// structured autocomplete for anything
const { suggestions } = useCopilotStructuredAutocompletion(
  {
    instructions: `Autocomplete or modify spreadsheet rows based on the inferred user intent.`,
    value: { rows: spreadsheet.rows.map((row) => ({ cells: row })) },
    enabled: !!activeCell && !spreadsheetIsEmpty,
  },
  [activeCell, spreadsheet]
);
```

----------------------------------------

TITLE: Handling Conditional Interrupts with useLangGraphInterrupt TSX
DESCRIPTION: This TSX snippet demonstrates how to use the `useLangGraphInterrupt` hook in a React component to handle different types of interrupts conditionally. It shows how to use the `enabled` property to activate the hook only when the `eventValue.type` matches a specific value ('ask' in this case), allowing multiple hooks to coexist and manage different UI flows based on the interrupt type. It also includes example React components for handling 'approve' and 'ask' interactions.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/human-in-the-loop/interrupt-flow.mdx#_snippet_17

LANGUAGE: TSX
CODE:
```
import { useLangGraphInterrupt } from "@copilotkit/react-core";
// ...

const ApproveComponent = ({ content, onAnswer }: { content: string; onAnswer: (approved: boolean) => void }) => (
    // styles omitted for brevity
    <div>
        <h1>Do you approve?</h1>
        <button onClick={() => onAnswer(true)}>Approve</button>
        <button onClick={() => onAnswer(false)}>Reject</button>
    </div>
)

const AskComponent = ({ question, onAnswer }: { question: string; onAnswer: (answer: string) => void }) => (
// styles omitted for brevity
    <div>
        <p>{question}</p>
        <form onSubmit={(e) => {
            e.preventDefault();
            onAnswer((e.target as HTMLFormElement).response.value);
        }}>
            <input type="text" name="response" placeholder="Enter your response" />
            <button type="submit">Submit</button>
        </form>
    </div>
)

const YourMainContent = () => {
    // ...
    useLangGraphInterrupt({
        enabled: ({ eventValue }) => eventValue.type === 'ask',
        render: ({ event, resolve }) => (

```

----------------------------------------

TITLE: Configuring CopilotKit Provider with Agent Specification
DESCRIPTION: React/TypeScript code showing how to specify the agent property in the CopilotKit provider component to route requests to a specific agent.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/agent-native-app/step-4-agentic-chat-ui.mdx#2025-04-23_snippet_2

LANGUAGE: tsx
CODE:
```
// ...
<CopilotKit
  // ...
  agent="agent" // [!code ++]
>
  {...}
</CopilotKit>
```

----------------------------------------

TITLE: Implementing useLangGraphInterrupt Hook in React Frontend
DESCRIPTION: Implementation of human-in-the-loop functionality using CopilotKit's useLangGraphInterrupt hook in a React component. The code shows how to render a ProposalViewer component when the LangGraph is interrupted and handle user decisions.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/tutorials/agent-native-app/step-5-human-in-the-loop.mdx#2025-04-23_snippet_0

LANGUAGE: tsx
CODE:
```
// ...
import { useLangGraphInterrupt } from "@copilotkit/react-core"; // [!code ++]
// ...

export default function HomePage() {
    // ...
    const { state: researchState, setResearchState } = useResearch()

    const streamingSection = useStreamingContent(researchState);

    useLangGraphInterrupt<Proposal>({
      render: ({ resolve, event }) => {
        return <ProposalViewer
          proposal={event.value}
          onSubmit={(approved, proposal) => resolve(
            JSON.stringify({
              ...proposal,
              approved,
            })
          )}
        />
      }
    })
    // ...
}
```

----------------------------------------

TITLE: Using useCoAgent Hook for State Restoration
DESCRIPTION: Shows how to use the useCoAgent hook to retrieve the restored state of a specific agent within the thread context.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/coagents/persistence/loading-agent-state.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
const { state } = useCoAgent({name: "research_agent"});

// state will now be the state of research_agent in the thread id given above
```

----------------------------------------

TITLE: Integrating Mastra Agent Deployment with Copilot Runtime (TypeScript)
DESCRIPTION: This TypeScript snippet demonstrates how to integrate a Mastra Agent deployment into the `CopilotRuntime`. It uses `MastraClient` to connect to the local Mastra Agent server, retrieves the agents, and then configures the `CopilotRuntime` to expose these agents to the frontend application.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/mastra/quickstart.mdx#_snippet_7

LANGUAGE: typescript
CODE:
```
// ...
import { MastraClient } from "@mastra/client-js";
// ...
const mastra = new MastraClient({
    "http://localhost:4111",
});
const mastraAgents = await mastra.getAGUI();
const runtime = new CopilotRuntime({
    agents: mastraAgents,
});
// ...
```

----------------------------------------

TITLE: useCoAgent Hook Return Properties
DESCRIPTION: Shows the structure of properties returned by the useCoAgent hook, including state management and control functions.
SOURCE: https://github.com/copilotkit/copilotkit/blob/main/docs/content/docs/reference/hooks/useCoAgent.mdx#2025-04-23_snippet_1

LANGUAGE: tsx
CODE:
```
const {
  name,     // The name of the agent currently being used.
  nodeName, // The name of the current LangGraph node.
  state,    // The current state of the agent.
  setState, // A function to update the state of the agent.
  running,  // A boolean indicating if the agent is currently running.
  start,    // A function to start the agent.
  stop,     // A function to stop the agent.
  run,      // A function to re-run the agent. Takes a HintFunction to inform the agent why it is being re-run.
} = agent;
```
          