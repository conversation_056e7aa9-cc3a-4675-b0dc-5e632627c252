package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/octopus"
	"gobackend-hvac-kratos/internal/transcription"
)

var (
	// Name is the name of the compiled software.
	Name = "octopus-interface"
	// Version is the version of the compiled software.
	Version = "v1.0.0"
	
	flagconf = flag.String("conf", "../../configs/octopus.yaml", "config path, eg: -conf config.yaml")
)

func main() {
	flag.Parse()
	
	// Initialize logger
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", Name,
		"service.name", Name,
		"service.version", Version,
	)
	
	log := log.NewHelper(logger)
	
	log.Infof("🐙 Starting Morphic Octopus Interface %s", Version)
	
	// Load configuration
	c := config.New(
		config.WithSource(
			file.NewSource(*flagconf),
		),
	)
	defer c.Close()
	
	if err := c.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	
	// Initialize database connection
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// Initialize services
	services, err := initServices(db, logger)
	if err != nil {
		log.Fatalf("Failed to initialize services: %v", err)
	}
	
	// Initialize Octopus configuration
	octopusConfig := &octopus.OctopusConfig{
		HTTPPort:         8083,
		WebSocketEnabled: true,
		DashboardPath:    "/dashboard",
		AuthEnabled:      false,
		AdminUsers:       []string{"<EMAIL>"},
		RefreshInterval:  5 * time.Second,
		MaxConnections:   100,
	}
	
	// Create Morphic Octopus Interface
	octopusInterface := octopus.NewMorphicOctopusInterface(
		db,
		services.EmailService,
		services.TranscriptionService,
		services.CustomerService,
		services.AIService,
		octopusConfig,
		logger,
	)
	
	// Start the interface
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	
	if err := octopusInterface.Start(ctx); err != nil {
		log.Fatalf("Failed to start Octopus Interface: %v", err)
	}
	
	log.Info("🐙 Morphic Octopus Interface started successfully!")
	log.Infof("🌐 Dashboard available at: http://localhost:%d/dashboard", octopusConfig.HTTPPort)
	log.Infof("🔌 WebSocket endpoint: ws://localhost:%d/api/dashboard/ws", octopusConfig.HTTPPort)
	
	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	log.Info("🐙 Shutting down Morphic Octopus Interface...")
	
	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()
	
	if err := octopusInterface.Stop(shutdownCtx); err != nil {
		log.Errorf("Failed to shutdown Octopus Interface: %v", err)
	}
	
	log.Info("🐙 Morphic Octopus Interface shutdown complete")
}