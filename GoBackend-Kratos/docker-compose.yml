version: '3.8'

services:
  # PostgreSQL Database (Shared by HVAC and BillionMail)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: hvac_db
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-billionmail.sql:/docker-entrypoint-initdb.d/init-billionmail.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (Shared by HVAC and BillionMail)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # BillionMail Core Management
  billionmail-core:
    image: billionmail/core:1.4
    hostname: billionmail-core
    depends_on:
      - postgres
      - redis
    ports:
      - "8090:80"    # BillionMail Web UI
      - "8091:443"   # BillionMail HTTPS
    environment:
      - TZ=UTC
      - DBNAME=hvac_db
      - DBUSER=hvac_user
      - DBPASS=hvac_password
      - REDISPASS=
      - BILLIONMAIL_HOSTNAME=localhost
    volumes:
      - billionmail_core_data:/opt/billionmail/core/data
      - billionmail_logs:/opt/billionmail/logs
      - /var/run/docker.sock:/var/run/docker.sock:ro
    restart: unless-stopped

  # BillionMail Postfix (SMTP)
  billionmail-postfix:
    image: billionmail/postfix:1.2
    hostname: billionmail-postfix
    depends_on:
      - postgres
      - redis
    ports:
      - "25:25"      # SMTP
      - "465:465"    # SMTPS
      - "587:587"    # SMTP Submission
    environment:
      - TZ=UTC
      - DBNAME=hvac_db
      - DBUSER=hvac_user
      - DBPASS=hvac_password
      - REDISPASS=
      - BILLIONMAIL_HOSTNAME=localhost
    volumes:
      - billionmail_postfix_data:/var/spool/postfix
      - billionmail_logs:/var/log/mail
    cap_add:
      - NET_BIND_SERVICE
    restart: unless-stopped

  # BillionMail Dovecot (IMAP/POP3)
  billionmail-dovecot:
    image: billionmail/dovecot:1.2
    hostname: billionmail-dovecot
    depends_on:
      - postgres
      - redis
      - billionmail-postfix
    ports:
      - "143:143"    # IMAP
      - "993:993"    # IMAPS
      - "110:110"    # POP3
      - "995:995"    # POP3S
    environment:
      - TZ=UTC
      - DBNAME=hvac_db
      - DBUSER=hvac_user
      - DBPASS=hvac_password
      - REDISPASS=
      - BILLIONMAIL_HOSTNAME=localhost
    volumes:
      - billionmail_vmail_data:/var/vmail
      - billionmail_logs:/var/log/mail
    cap_add:
      - NET_BIND_SERVICE
    restart: unless-stopped

  # HVAC Kratos Backend
  hvac-backend:
    build: .
    ports:
      - "8080:8080"  # HTTP
      - "9000:9000"  # gRPC
      - "8081:8081"  # MCP
    environment:
      - DATABASE_URL=************************************************/hvac_db?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - BILLIONMAIL_API_URL=http://billionmail-core:80/api/v1
      - BILLIONMAIL_SMTP_HOST=billionmail-postfix
      - BILLIONMAIL_SMTP_PORT=587
      - BILLIONMAIL_IMAP_HOST=billionmail-dovecot
      - BILLIONMAIL_IMAP_PORT=143
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      billionmail-core:
        condition: service_started
      billionmail-postfix:
        condition: service_started
      billionmail-dovecot:
        condition: service_started
    volumes:
      - ./configs:/root/configs
    restart: unless-stopped

  # Bytebase Database Management
  bytebase:
    image: bytebase/bytebase:latest
    hostname: bytebase
    ports:
      - "8092:8080"  # Bytebase Web UI
    environment:
      - BB_DATA_DIR=/var/opt/bytebase
      - BB_EXTERNAL_URL=http://localhost:8092
      - BB_PG_URL=************************************************/hvac_db?sslmode=disable
      - BB_PORT=8080
      - BB_READONLY=false
    volumes:
      - bytebase_data:/var/opt/bytebase
      - ./migrations:/migrations
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # 🐙 Morphic Octopus Interface - Ultimate Backend Management
  octopus-interface:
    build:
      context: .
      dockerfile: Dockerfile.octopus
    ports:
      - "8083:8083"
    environment:
      - DATABASE_URL=************************************************/hvac_db?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - OLLAMA_URL=http://host.docker.internal:11434
      - EMAIL_SERVICE_URL=http://email-intelligence:8082
      - TRANSCRIPTION_SERVICE_URL=http://hvac-backend:8084
    depends_on:
      - postgres
      - redis
      - hvac-backend
    volumes:
      - ./logs:/app/logs
      - ./configs:/app/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/api/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  billionmail_core_data:
  billionmail_postfix_data:
  billionmail_vmail_data:
  billionmail_logs:
  bytebase_data: