server:
  http:
    addr: 0.0.0.0:8080
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s

data:
  database:
    driver: postgres
    source: postgres://user:password@localhost:5432/hvac_db?sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

ai:
  models:
    gemma:
      endpoint: "http://localhost:11434"
      model_name: "gemma:3b-instruct-q4_0"
      max_tokens: 4096
    bielik:
      endpoint: "http://localhost:11435"
      model_name: "bielik-v3"
      max_tokens: 32768

  # 🚀 Enhanced AI Configuration
  langchain:
    enabled: true
    default_model: "gemma:3b-instruct-q4_0"
    temperature: 0.7
    max_tokens: 4096
    timeout: "120s"
    retry_attempts: 3
    cache_enabled: true
    cache_ttl: "1h"

  vector_db:
    type: "chromem"
    enabled: true
    persistence: true
    storage_path: "/data/vectordb"
    embedding_model: "nomic-embed-text"
    collections:
      - name: "hvac-knowledge"
        description: "HVAC technical knowledge and troubleshooting guides"
        embedding_model: "nomic-embed-text"
        max_documents: 10000
      - name: "customer-emails"
        description: "Customer email history for similarity matching"
        embedding_model: "nomic-embed-text"
        max_documents: 50000
      - name: "knowledge-base"
        description: "General company knowledge base"
        embedding_model: "nomic-embed-text"
        max_documents: 25000
      - name: "customer-data"
        description: "Customer interaction history and preferences"
        embedding_model: "nomic-embed-text"
        max_documents: 100000

  ollama_farm:
    enabled: true
    load_balancing: "round_robin"
    health_check_interval: "30s"
    max_retries: 3
    timeout: "120s"
    nodes:
      - endpoint: "http://localhost:11434"
        models: ["gemma:3b-instruct-q4_0", "nomic-embed-text"]
        weight: 1
        max_concurrent: 10
      - endpoint: "http://localhost:11435"
        models: ["bielik-v3"]
        weight: 1
        max_concurrent: 5

  performance:
    json_iterator:
      enabled: true
      config: "compatible"
      fast_mode: false

    caching:
      enabled: true
      max_size: 1000
      ttl: "1h"
      cleanup_interval: "10m"

    metrics:
      enabled: true
      collection_interval: "30s"
      retention_period: "24h"

email:
  billionmail:
    api_url: "http://billionmail-core:80/api/v1"
    web_ui_url: "http://localhost:8090"
    admin_email: "<EMAIL>"
    admin_password: "hvac_admin_2024"
  smtp:
    host: "billionmail-postfix"
    port: 587
    username: "<EMAIL>"
    password: "hvac_password_2024"
    from: "<EMAIL>"
    use_tls: true
  imap:
    host: "billionmail-dovecot"
    port: 143
    username: "<EMAIL>"
    password: "hvac_password_2024"
    use_tls: false
  templates:
    service_reminder: "service_reminder"
    quote_follow_up: "quote_follow_up"
    invoice_notification: "invoice_notification"
    appointment_confirmation: "appointment_confirmation"

mcp:
  server:
    addr: 0.0.0.0:8081
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true

logging:
  level: "info"
  format: "json"
  output: "stdout"

tracing:
  endpoint: "http://localhost:14268/api/traces"
  sampler: 1.0

metrics:
  addr: 0.0.0.0:9090
  path: "/metrics"