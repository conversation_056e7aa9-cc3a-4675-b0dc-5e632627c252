// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.12.4
// source: api/ai/v1/ai.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ChatRequest represents a chat request
type ChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Model   string   `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	Context []string `protobuf:"bytes,3,rep,name=context,proto3" json:"context,omitempty"`
}

func (x *ChatRequest) Reset() {
	*x = ChatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatRequest) ProtoMessage() {}

func (x *ChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatRequest.ProtoReflect.Descriptor instead.
func (*ChatRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{0}
}

func (x *ChatRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ChatRequest) GetContext() []string {
	if x != nil {
		return x.Context
	}
	return nil
}

// ChatResponse represents a chat response
type ChatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response   string `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	ModelUsed  string `protobuf:"bytes,2,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	TokensUsed int32  `protobuf:"varint,3,opt,name=tokens_used,json=tokensUsed,proto3" json:"tokens_used,omitempty"`
}

func (x *ChatResponse) Reset() {
	*x = ChatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatResponse) ProtoMessage() {}

func (x *ChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatResponse.ProtoReflect.Descriptor instead.
func (*ChatResponse) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{1}
}

func (x *ChatResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *ChatResponse) GetModelUsed() string {
	if x != nil {
		return x.ModelUsed
	}
	return ""
}

func (x *ChatResponse) GetTokensUsed() int32 {
	if x != nil {
		return x.TokensUsed
	}
	return 0
}

// AnalyzeRequest represents an analysis request
type AnalyzeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content      string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	AnalysisType string `protobuf:"bytes,2,opt,name=analysis_type,json=analysisType,proto3" json:"analysis_type,omitempty"`
	Model        string `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *AnalyzeRequest) Reset() {
	*x = AnalyzeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyzeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeRequest) ProtoMessage() {}

func (x *AnalyzeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeRequest.ProtoReflect.Descriptor instead.
func (*AnalyzeRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{2}
}

func (x *AnalyzeRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AnalyzeRequest) GetAnalysisType() string {
	if x != nil {
		return x.AnalysisType
	}
	return ""
}

func (x *AnalyzeRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

// AnalyzeResponse represents an analysis response
type AnalyzeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Analysis   string            `protobuf:"bytes,1,opt,name=analysis,proto3" json:"analysis,omitempty"`
	Confidence float32           `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Metadata   map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AnalyzeResponse) Reset() {
	*x = AnalyzeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyzeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeResponse) ProtoMessage() {}

func (x *AnalyzeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeResponse.ProtoReflect.Descriptor instead.
func (*AnalyzeResponse) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{3}
}

func (x *AnalyzeResponse) GetAnalysis() string {
	if x != nil {
		return x.Analysis
	}
	return ""
}

func (x *AnalyzeResponse) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *AnalyzeResponse) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// ListModelsRequest represents a request to list available models
type ListModelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListModelsRequest) Reset() {
	*x = ListModelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsRequest) ProtoMessage() {}

func (x *ListModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsRequest.ProtoReflect.Descriptor instead.
func (*ListModelsRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{4}
}

// ListModelsResponse represents a response with available models
type ListModelsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*AIModel `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *ListModelsResponse) Reset() {
	*x = ListModelsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsResponse) ProtoMessage() {}

func (x *ListModelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsResponse.ProtoReflect.Descriptor instead.
func (*ListModelsResponse) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{5}
}

func (x *ListModelsResponse) GetModels() []*AIModel {
	if x != nil {
		return x.Models
	}
	return nil
}

// AIModel represents an AI model
type AIModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type      string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Available bool   `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	Endpoint  string `protobuf:"bytes,4,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (x *AIModel) Reset() {
	*x = AIModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AIModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIModel) ProtoMessage() {}

func (x *AIModel) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIModel.ProtoReflect.Descriptor instead.
func (*AIModel) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{6}
}

func (x *AIModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AIModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AIModel) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *AIModel) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

var file_api_ai_v1_ai_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x22,
	0x5b, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x6c, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x75, 0x73,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55,
	0x73, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x5f, 0x75, 0x73,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x55, 0x73, 0x65, 0x64, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x6f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2d, 0x68, 0x76, 0x61, 0x63, 0x2d, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var file_api_ai_v1_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 7)

func file_api_ai_v1_ai_proto_rawDescGZIP() []byte {
	return file_api_ai_v1_ai_proto_rawDesc
}

// AIServiceClient is the client API for AIService service.
type AIServiceClient interface {
	Chat(ctx context.Context, in *ChatRequest, opts ...grpc.CallOption) (*ChatResponse, error)
	Analyze(ctx context.Context, in *AnalyzeRequest, opts ...grpc.CallOption) (*AnalyzeResponse, error)
	ListModels(ctx context.Context, in *ListModelsRequest, opts ...grpc.CallOption) (*ListModelsResponse, error)
}

type aiServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAIServiceClient(cc grpc.ClientConnInterface) AIServiceClient {
	return &aiServiceClient{cc}
}

func (c *aiServiceClient) Chat(ctx context.Context, in *ChatRequest, opts ...grpc.CallOption) (*ChatResponse, error) {
	out := new(ChatResponse)
	err := c.cc.Invoke(ctx, "/api.ai.v1.AIService/Chat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiServiceClient) Analyze(ctx context.Context, in *AnalyzeRequest, opts ...grpc.CallOption) (*AnalyzeResponse, error) {
	out := new(AnalyzeResponse)
	err := c.cc.Invoke(ctx, "/api.ai.v1.AIService/Analyze", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiServiceClient) ListModels(ctx context.Context, in *ListModelsRequest, opts ...grpc.CallOption) (*ListModelsResponse, error) {
	out := new(ListModelsResponse)
	err := c.cc.Invoke(ctx, "/api.ai.v1.AIService/ListModels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AIServiceServer is the server API for AIService service.
type AIServiceServer interface {
	Chat(context.Context, *ChatRequest) (*ChatResponse, error)
	Analyze(context.Context, *AnalyzeRequest) (*AnalyzeResponse, error)
	ListModels(context.Context, *ListModelsRequest) (*ListModelsResponse, error)
}

// UnimplementedAIServiceServer can be embedded to have forward compatible implementations.
type UnimplementedAIServiceServer struct {
}

func (*UnimplementedAIServiceServer) Chat(context.Context, *ChatRequest) (*ChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Chat not implemented")
}

func (*UnimplementedAIServiceServer) Analyze(context.Context, *AnalyzeRequest) (*AnalyzeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Analyze not implemented")
}

func (*UnimplementedAIServiceServer) ListModels(context.Context, *ListModelsRequest) (*ListModelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListModels not implemented")
}

func RegisterAIServiceServer(s *grpc.Server, srv AIServiceServer) {
	s.RegisterService(&_AIService_serviceDesc, srv)
}

var _AIService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "api.ai.v1.AIService",
	HandlerType: (*AIServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Chat",
			Handler:    _AIService_Chat_Handler,
		},
		{
			MethodName: "Analyze",
			Handler:    _AIService_Analyze_Handler,
		},
		{
			MethodName: "ListModels",
			Handler:    _AIService_ListModels_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/ai/v1/ai.proto",
}

func _AIService_Chat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIServiceServer).Chat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.ai.v1.AIService/Chat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIServiceServer).Chat(ctx, req.(*ChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIService_Analyze_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyzeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIServiceServer).Analyze(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.ai.v1.AIService/Analyze",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIServiceServer).Analyze(ctx, req.(*AnalyzeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIService_ListModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListModelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIServiceServer).ListModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.ai.v1.AIService/ListModels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIServiceServer).ListModels(ctx, req.(*ListModelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}
