# 🚀 GoBackend HVAC Kratos

## 🎯 Ultra-Modern HVAC CRM with Kratos Framework

### ✨ Features

- 🔥 **Kratos Framework** - Production-ready microservice architecture
- 🤖 **AI Integration** - Gemma-3-4b-it & Bielik V3 models
- 🛠️ **MCP Protocol** - Type-safe LLM tool integration
- 📧 **BillionMail Ready** - Advanced email management
- 🗄️ **PostgreSQL** - Robust database with GORM
- ⚡ **Redis Cache** - High-performance caching
- 🔍 **Jaeger Tracing** - Distributed tracing
- 📊 **Prometheus Metrics** - Comprehensive monitoring
- 🐳 **Docker** - Containerized deployment
- 🔧 **gRPC & HTTP** - Dual transport protocols

### 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP/gRPC     │    │   MCP Server    │    │   AI Models     │
│   Transport     │    │   (LLM Tools)   │    │ Gemma/Bielik    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ HVAC Service│  │ AI Service  │  │ MCP Service │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                   Business Logic                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ Customer UC │  │   Job UC    │  │   AI UC     │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                    Data Layer                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ PostgreSQL  │  │   Redis     │  │ AI Models   │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```### 🚀 Quick Start

#### Prerequisites
- Go 1.24+
- Docker & Docker Compose
- Protocol Buffers compiler
- NVIDIA GPU (optional, for AI acceleration)

#### 1. Clone & Setup
```bash
git clone <repository>
cd GoBackend-Kratos
chmod +x scripts/*.sh
./scripts/build.sh  # Install required tools & build
```

#### 2. Complete Enterprise Deployment
```bash
./scripts/deploy-with-bytebase.sh  # Full deployment with all services
```

#### 3. Individual Service Deployments
```bash
./scripts/deploy-with-billionmail.sh  # Email integration
./scripts/deploy-with-ai.sh          # AI models only
./scripts/deploy.sh                  # Basic HVAC system
```

#### 4. Development Mode
```bash
# Start dependencies
docker-compose up -d postgres redis jaeger bytebase
docker-compose -f docker-compose.ai.yml up -d ollama

# Setup AI model
./scripts/setup-gemma.sh

# Run locally
go run cmd/server/main.go -conf ./configs
```

### 🔧 API Endpoints

#### HTTP REST API
- `GET /api/v1/customers` - List customers
- `POST /api/v1/customers` - Create customer
- `GET /api/v1/jobs` - List jobs
- `POST /api/v1/jobs` - Create job
- `POST /api/v1/ai/chat` - AI chat
- `POST /api/v1/ai/analyze` - AI analysis

#### gRPC Services
- `HVACService` - Customer & Job management
- `AIService` - AI model interactions

#### MCP Tools (for LLM)
- `create_customer` - Create HVAC customer
- `create_job` - Create HVAC job
- `send_email` - Send emails via BillionMail
- `ai_analyze` - Analyze content with AI
- `hvac_advice` - Get professional HVAC advice using Gemma AI

#### 📧 Email Services
- `POST /api/v1/emails/send` - Send emails via BillionMail
- `GET /api/v1/emails` - List emails
- `POST /api/v1/campaigns` - Create email campaigns
- `POST /api/v1/emails/analyze-sentiment` - Email sentiment analysis

#### 🤖 AI Integration
- `POST /api/v1/ai/chat` - Chat with Gemma-3-4b-it-qat-q4_0-gguf
- `POST /api/v1/ai/analyze` - Analyze content (sentiment, HVAC issues)
- `GET /api/v1/ai/models` - List available AI models
- Ollama Web UI: http://localhost:3000

### 🎯 Complete Service URLs

#### **🏠 Main Services:**
- **HVAC Backend:** http://localhost:8080
- **gRPC Server:** localhost:9000
- **MCP Server:** localhost:8081

#### **📧 Email Services:**
- **BillionMail UI:** http://localhost:8090
- **SMTP Server:** localhost:587
- **IMAP Server:** localhost:143
- **Webmail:** http://localhost:8090/roundcube

#### **🗄️ Database Management:**
- **Bytebase UI:** http://localhost:8092
- **Schema Browser:** http://localhost:8092/db
- **Migration Center:** http://localhost:8092/migration

#### **📊 Monitoring:**
- **Jaeger Tracing:** http://localhost:16686

### 🎯 Enterprise Features

✅ **Kratos Framework** - Production-ready microservice architecture
✅ **BillionMail Integration** - Professional email automation
✅ **Bytebase Management** - Enterprise database operations
✅ **AI Models** - Gemma-3-4b-it & Bielik V3 integration
✅ **MCP Protocol** - Type-safe LLM tools
✅ **Docker Deployment** - Complete containerization

Ready for enterprise HVAC operations! 🚀🔥📧🗄️