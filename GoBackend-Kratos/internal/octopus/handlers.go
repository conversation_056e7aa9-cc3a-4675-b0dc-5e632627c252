package octopus

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

// 🌐 Setup HTTP Server with Octopus Routes
func (o *MorphicOctopusInterface) setupHTTPServer() {
	router := mux.NewRouter()
	
	// Enable CORS
	router.Use(o.corsMiddleware)
	router.Use(o.loggingMiddleware)
	
	// Dashboard routes
	router.HandleFunc("/", o.handleDashboard).Methods("GET")
	router.HandleFunc("/dashboard", o.handleDashboard).Methods("GET")
	router.HandleFunc("/api/dashboard/data", o.handleDashboardData).Methods("GET")
	router.HandleFunc("/api/dashboard/ws", o.handleWebSocket)
	
	// System management routes
	o.setupSystemRoutes(router)
	
	// Service management routes
	o.setupServiceRoutes(router)
	
	// Customer intelligence routes
	o.setupCustomerRoutes(router)
	
	// Transcription management routes
	o.setupTranscriptionRoutes(router)
	
	// Email intelligence routes
	o.setupEmailRoutes(router)
	
	// AI management routes
	o.setupAIRoutes(router)
	
	// Static files
	router.PathPrefix("/static/").Handler(http.StripPrefix("/static/", 
		http.FileServer(http.Dir("./web/octopus/static/"))))
	
	o.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", o.config.HTTPPort),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	o.log.Infof("🐙 Morphic Octopus Interface configured on port %d", o.config.HTTPPort)
}

// 🎛️ Setup System Management Routes
func (o *MorphicOctopusInterface) setupSystemRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/system").Subrouter()
	
	api.HandleFunc("/status", o.handleSystemStatus).Methods("GET")
	api.HandleFunc("/health", o.handleSystemHealth).Methods("GET")
	api.HandleFunc("/metrics", o.handleSystemMetrics).Methods("GET")
	api.HandleFunc("/logs", o.handleSystemLogs).Methods("GET")
	api.HandleFunc("/restart", o.handleSystemRestart).Methods("POST")
	api.HandleFunc("/backup", o.handleSystemBackup).Methods("POST")
	api.HandleFunc("/maintenance", o.handleMaintenanceMode).Methods("POST")
}

// 🔧 Setup Service Management Routes
func (o *MorphicOctopusInterface) setupServiceRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/services").Subrouter()
	
	api.HandleFunc("/health", o.handleServicesHealth).Methods("GET")
	api.HandleFunc("/{service}/start", o.handleServiceStart).Methods("POST")
	api.HandleFunc("/{service}/stop", o.handleServiceStop).Methods("POST")
	api.HandleFunc("/{service}/restart", o.handleServiceRestart).Methods("POST")
	api.HandleFunc("/{service}/config", o.handleServiceConfig).Methods("GET", "PUT")
	api.HandleFunc("/{service}/logs", o.handleServiceLogs).Methods("GET")
}

// 👥 Setup Customer Intelligence Routes
func (o *MorphicOctopusInterface) setupCustomerRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/customers").Subrouter()
	
	api.HandleFunc("/metrics", o.handleCustomerMetrics).Methods("GET")
	api.HandleFunc("/segments", o.handleCustomerSegments).Methods("GET", "POST")
	api.HandleFunc("/intelligence/{id}", o.handleCustomerIntelligence).Methods("GET")
	api.HandleFunc("/search", o.handleCustomerSearch).Methods("POST")
	api.HandleFunc("/analytics/refresh", o.handleCustomerAnalyticsRefresh).Methods("POST")
	api.HandleFunc("/export", o.handleCustomerExport).Methods("POST")
}

// 📞 Setup Transcription Management Routes
func (o *MorphicOctopusInterface) setupTranscriptionRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/transcription").Subrouter()
	
	api.HandleFunc("/stats", o.handleTranscriptionStats).Methods("GET")
	api.HandleFunc("/sources", o.handleTranscriptionSources).Methods("GET", "POST")
	api.HandleFunc("/sources/{id}", o.handleTranscriptionSource).Methods("GET", "PUT", "DELETE")
	api.HandleFunc("/calls", o.handleTranscriptionCalls).Methods("GET")
	api.HandleFunc("/calls/{id}", o.handleTranscriptionCall).Methods("GET")
	api.HandleFunc("/calls/{id}/reprocess", o.handleTranscriptionReprocess).Methods("POST")
	api.HandleFunc("/process", o.handleTranscriptionProcess).Methods("POST")
}

// 📧 Setup Email Intelligence Routes
func (o *MorphicOctopusInterface) setupEmailRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/email").Subrouter()
	
	api.HandleFunc("/intelligence", o.handleEmailIntelligence).Methods("GET")
	api.HandleFunc("/campaigns", o.handleEmailCampaigns).Methods("GET", "POST")
	api.HandleFunc("/templates", o.handleEmailTemplates).Methods("GET", "POST")
	api.HandleFunc("/analytics", o.handleEmailAnalytics).Methods("GET")
	api.HandleFunc("/mailboxes", o.handleEmailMailboxes).Methods("GET", "POST")
	api.HandleFunc("/send", o.handleEmailSend).Methods("POST")
}

// 🤖 Setup AI Management Routes
func (o *MorphicOctopusInterface) setupAIRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/ai").Subrouter()
	
	api.HandleFunc("/performance", o.handleAIPerformance).Methods("GET")
	api.HandleFunc("/models", o.handleAIModels).Methods("GET")
	api.HandleFunc("/models/{model}/status", o.handleAIModelStatus).Methods("GET")
	api.HandleFunc("/models/{model}/restart", o.handleAIModelRestart).Methods("POST")
	api.HandleFunc("/analyze", o.handleAIAnalyze).Methods("POST")
	api.HandleFunc("/train", o.handleAITrain).Methods("POST")
	api.HandleFunc("/queue", o.handleAIQueue).Methods("GET")
}

// 🚀 Start the Morphic Octopus Interface
func (o *MorphicOctopusInterface) Start(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🐙 Starting Morphic Octopus Interface...")
	
	// Start real-time data broadcasting
	if o.config.WebSocketEnabled {
		go o.startRealtimeBroadcast(ctx)
	}
	
	// Start HTTP server
	go func() {
		o.log.Infof("🌐 Octopus Interface listening on port %d", o.config.HTTPPort)
		if err := o.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			o.log.Errorf("❌ Octopus HTTP server error: %v", err)
		}
	}()
	
	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface started successfully!")
	return nil
}

// 🛑 Stop the Morphic Octopus Interface
func (o *MorphicOctopusInterface) Stop(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🐙 Stopping Morphic Octopus Interface...")
	
	// Close WebSocket connections
	for id, conn := range o.wsConnections {
		conn.Close()
		delete(o.wsConnections, id)
	}
	
	// Stop HTTP server
	if o.httpServer != nil {
		ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
		
		if err := o.httpServer.Shutdown(ctx); err != nil {
			o.log.WithContext(ctx).Errorf("❌ Octopus HTTP server shutdown error: %v", err)
		}
	}
	
	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface stopped")
	return nil
}

// 📊 Handle Dashboard Data Request
func (o *MorphicOctopusInterface) handleDashboardData(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	dashboard, err := o.buildDashboardData(ctx)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to build dashboard data: %v", err), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(dashboard); err != nil {
		o.log.Errorf("Failed to encode dashboard data: %v", err)
	}
}

// 🏠 Handle Dashboard HTML
func (o *MorphicOctopusInterface) handleDashboard(w http.ResponseWriter, r *http.Request) {
	dashboardHTML := o.generateDashboardHTML()
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(dashboardHTML))
}

// 🔌 Handle WebSocket Connection
func (o *MorphicOctopusInterface) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := o.wsUpgrader.Upgrade(w, r, nil)
	if err != nil {
		o.log.Errorf("WebSocket upgrade failed: %v", err)
		return
	}
	
	connectionID := fmt.Sprintf("conn_%d", time.Now().UnixNano())
	o.wsConnections[connectionID] = conn
	
	o.log.Infof("🔌 New WebSocket connection: %s", connectionID)
	
	// Handle connection cleanup
	defer func() {
		conn.Close()
		delete(o.wsConnections, connectionID)
		o.log.Infof("🔌 WebSocket connection closed: %s", connectionID)
	}()
	
	// Keep connection alive and handle messages
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}
}

// 📡 Start Real-time Data Broadcasting
func (o *MorphicOctopusInterface) startRealtimeBroadcast(ctx context.Context) {
	ticker := time.NewTicker(o.config.RefreshInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			o.broadcastDashboardUpdate(ctx)
		}
	}
}

// 📢 Broadcast Dashboard Update to WebSocket Clients
func (o *MorphicOctopusInterface) broadcastDashboardUpdate(ctx context.Context) {
	if len(o.wsConnections) == 0 {
		return
	}
	
	dashboard, err := o.buildDashboardData(ctx)
	if err != nil {
		o.log.Errorf("Failed to build dashboard data for broadcast: %v", err)
		return
	}
	
	data, err := json.Marshal(dashboard)
	if err != nil {
		o.log.Errorf("Failed to marshal dashboard data: %v", err)
		return
	}
	
	// Broadcast to all connected clients
	for id, conn := range o.wsConnections {
		if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
			o.log.Warnf("Failed to send data to WebSocket %s: %v", id, err)
			conn.Close()
			delete(o.wsConnections, id)
		}
	}
}

// 📊 Build Dashboard Data
func (o *MorphicOctopusInterface) buildDashboardData(ctx context.Context) (*OctopusDashboard, error) {
	dashboard := &OctopusDashboard{
		Timestamp: time.Now(),
	}
	
	// Build system status
	systemStatus, err := o.buildSystemStatus(ctx)
	if err != nil {
		o.log.Warnf("Failed to build system status: %v", err)
	} else {
		dashboard.SystemStatus = systemStatus
	}
	
	// Build service health
	serviceHealth, err := o.buildServiceHealth(ctx)
	if err != nil {
		o.log.Warnf("Failed to build service health: %v", err)
	} else {
		dashboard.ServiceHealth = serviceHealth
	}
	
	// Build customer metrics
	customerMetrics, err := o.buildCustomerMetrics(ctx)
	if err != nil {
		o.log.Warnf("Failed to build customer metrics: %v", err)
	} else {
		dashboard.CustomerMetrics = customerMetrics
	}
	
	// Build transcription stats
	transcriptionStats, err := o.buildTranscriptionStats(ctx)
	if err != nil {
		o.log.Warnf("Failed to build transcription stats: %v", err)
	} else {
		dashboard.TranscriptionStats = transcriptionStats
	}
	
	// Build email intelligence
	emailIntelligence, err := o.buildEmailIntelligence(ctx)
	if err != nil {
		o.log.Warnf("Failed to build email intelligence: %v", err)
	} else {
		dashboard.EmailIntelligence = emailIntelligence
	}
	
	// Build AI performance
	aiPerformance, err := o.buildAIPerformance(ctx)
	if err != nil {
		o.log.Warnf("Failed to build AI performance: %v", err)
	} else {
		dashboard.AIPerformance = aiPerformance
	}
	
	// Build alerts and quick actions
	dashboard.RealtimeAlerts = o.buildRealtimeAlerts(ctx)
	dashboard.QuickActions = o.buildQuickActions()
	
	return dashboard, nil
}

// 🔧 Build System Status
func (o *MorphicOctopusInterface) buildSystemStatus(ctx context.Context) (*SystemStatus, error) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return &SystemStatus{
		Uptime:              time.Since(time.Now().Add(-time.Hour)), // Placeholder
		CPUUsage:            50.0, // Placeholder
		MemoryUsage:         float64(m.Alloc) / 1024 / 1024, // MB
		DatabaseConnections: 10, // Placeholder
		ActiveWebSockets:    len(o.wsConnections),
		TotalRequests:       1000, // Placeholder
		ErrorRate:           0.01, // Placeholder
		ResponseTime:        50 * time.Millisecond, // Placeholder
	}, nil
}

// 🏥 Build Service Health
func (o *MorphicOctopusInterface) buildServiceHealth(ctx context.Context) (*ServiceHealth, error) {
	return &ServiceHealth{
		EmailService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 25 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.9,
		},
		TranscriptionService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 100 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  98.5,
		},
		CustomerService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 15 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.8,
		},
		AIService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 200 * time.Millisecond,
			ErrorCount:   1,
			SuccessRate:  97.2,
		},
		DatabaseService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 5 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  99.9,
		},
		RedisService: &ServiceStatus{
			Status:       "healthy",
			LastCheck:    time.Now(),
			ResponseTime: 2 * time.Millisecond,
			ErrorCount:   0,
			SuccessRate:  100.0,
		},
	}, nil
}

// 🔧 Middleware functions
func (o *MorphicOctopusInterface) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}

func (o *MorphicOctopusInterface) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		next.ServeHTTP(w, r)
		o.log.Infof("🐙 %s %s %v", r.Method, r.URL.Path, time.Since(start))
	})
}