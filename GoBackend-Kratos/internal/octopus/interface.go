package octopus

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/transcription"
)

// 🐙 MORPHIC OCTOPUS INTERFACE - Potężny system zarządzania backendem
type MorphicOctopusInterface struct {
	log                *log.Helper
	db                 *gorm.DB
	httpServer         *http.Server
	wsUpgrader         websocket.Upgrader

	// Service Tentacles (Macki Usług)
	emailService       *email.EmailIntelligenceService
	transcriptionService *transcription.TranscriptionParser
	customerService    *customer.CustomerIntelligenceService
	aiService          *ai.Gemma3Service

	// 🔒 Thread-safe WebSocket connections with proper cleanup
	wsConnections      map[string]*websocket.Conn
	wsConnectionsMutex sync.RWMutex
	wsCleanupTicker    *time.Ticker

	// Configuration
	config             *OctopusConfig
}

// ⚙️ Octopus Configuration
type OctopusConfig struct {
	HTTPPort           int    `yaml:"http_port"`
	WebSocketEnabled   bool   `yaml:"websocket_enabled"`
	DashboardPath      string `yaml:"dashboard_path"`
	AuthEnabled        bool   `yaml:"auth_enabled"`
	AdminUsers         []string `yaml:"admin_users"`
	RefreshInterval    time.Duration `yaml:"refresh_interval"`
	MaxConnections     int    `yaml:"max_connections"`
}

// 📊 Octopus Dashboard Data
type OctopusDashboard struct {
	SystemStatus       *SystemStatus       `json:"system_status"`
	ServiceHealth      *ServiceHealth      `json:"service_health"`
	CustomerMetrics    *CustomerMetrics    `json:"customer_metrics"`
	TranscriptionStats *TranscriptionStats `json:"transcription_stats"`
	EmailIntelligence  *EmailIntelligence  `json:"email_intelligence"`
	AIPerformance      *AIPerformance      `json:"ai_performance"`
	RealtimeAlerts     []*Alert           `json:"realtime_alerts"`
	QuickActions       []*QuickAction     `json:"quick_actions"`
	Timestamp          time.Time          `json:"timestamp"`
}

// 🔧 System Status
type SystemStatus struct {
	Uptime             time.Duration `json:"uptime"`
	CPUUsage           float64       `json:"cpu_usage"`
	MemoryUsage        float64       `json:"memory_usage"`
	DatabaseConnections int          `json:"database_connections"`
	ActiveWebSockets   int           `json:"active_websockets"`
	TotalRequests      int64         `json:"total_requests"`
	ErrorRate          float64       `json:"error_rate"`
	ResponseTime       time.Duration `json:"avg_response_time"`
}

// 🏥 Service Health
type ServiceHealth struct {
	EmailService       *ServiceStatus `json:"email_service"`
	TranscriptionService *ServiceStatus `json:"transcription_service"`
	CustomerService    *ServiceStatus `json:"customer_service"`
	AIService          *ServiceStatus `json:"ai_service"`
	DatabaseService    *ServiceStatus `json:"database_service"`
	RedisService       *ServiceStatus `json:"redis_service"`
}

// 📈 Service Status
type ServiceStatus struct {
	Status             string        `json:"status"` // healthy, degraded, unhealthy
	LastCheck          time.Time     `json:"last_check"`
	ResponseTime       time.Duration `json:"response_time"`
	ErrorCount         int           `json:"error_count"`
	SuccessRate        float64       `json:"success_rate"`
	Message            string        `json:"message,omitempty"`
}

// 👥 Customer Metrics
type CustomerMetrics struct {
	TotalCustomers     int     `json:"total_customers"`
	NewToday           int     `json:"new_today"`
	NewThisWeek        int     `json:"new_this_week"`
	ActiveCustomers    int     `json:"active_customers"`
	HighValueCustomers int     `json:"high_value_customers"`
	AtRiskCustomers    int     `json:"at_risk_customers"`
	AvgSatisfaction    float64 `json:"avg_satisfaction"`
	ChurnRate          float64 `json:"churn_rate"`
	LifetimeValue      float64 `json:"avg_lifetime_value"`
}

// 📞 Transcription Statistics
type TranscriptionStats struct {
	TotalCalls         int     `json:"total_calls"`
	CallsToday         int     `json:"calls_today"`
	CallsThisWeek      int     `json:"calls_this_week"`
	HVACRelevantCalls  int     `json:"hvac_relevant_calls"`
	EmergencyCalls     int     `json:"emergency_calls"`
	AvgCallDuration    time.Duration `json:"avg_call_duration"`
	AvgConfidence      float64 `json:"avg_confidence"`
	ProcessingBacklog  int     `json:"processing_backlog"`
	TopCallerCompanies []string `json:"top_caller_companies"`
}

// 📧 Email Intelligence
type EmailIntelligence struct {
	TotalEmails        int     `json:"total_emails"`
	EmailsToday        int     `json:"emails_today"`
	EmailsThisWeek     int     `json:"emails_this_week"`
	HVACRelevantEmails int     `json:"hvac_relevant_emails"`
	PositiveSentiment  int     `json:"positive_sentiment"`
	NegativeSentiment  int     `json:"negative_sentiment"`
	AvgProcessingTime  time.Duration `json:"avg_processing_time"`
	TopKeywords        []string `json:"top_keywords"`
}

// 🤖 AI Performance
type AIPerformance struct {
	TotalRequests      int64         `json:"total_requests"`
	RequestsToday      int64         `json:"requests_today"`
	AvgResponseTime    time.Duration `json:"avg_response_time"`
	SuccessRate        float64       `json:"success_rate"`
	ModelAccuracy      float64       `json:"model_accuracy"`
	TokensProcessed    int64         `json:"tokens_processed"`
	ActiveModels       []string      `json:"active_models"`
	QueueLength        int           `json:"queue_length"`
}

// 🚨 Alert System
type Alert struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"` // info, warning, error, critical
	Title       string    `json:"title"`
	Message     string    `json:"message"`
	Source      string    `json:"source"`
	Timestamp   time.Time `json:"timestamp"`
	Acknowledged bool     `json:"acknowledged"`
	Actions     []string  `json:"actions,omitempty"`
}

// ⚡ Quick Actions
type QuickAction struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Endpoint    string `json:"endpoint"`
	Method      string `json:"method"`
	Category    string `json:"category"`
	Dangerous   bool   `json:"dangerous,omitempty"`
}

// NewMorphicOctopusInterface creates the ultimate backend management interface
func NewMorphicOctopusInterface(
	db *gorm.DB,
	emailService *email.EmailIntelligenceService,
	transcriptionService *transcription.TranscriptionParser,
	customerService *customer.CustomerIntelligenceService,
	aiService *ai.Gemma3Service,
	config *OctopusConfig,
	logger log.Logger,
) *MorphicOctopusInterface {
	log := log.NewHelper(logger)

	octopus := &MorphicOctopusInterface{
		log:                  log,
		db:                   db,
		emailService:         emailService,
		transcriptionService: transcriptionService,
		customerService:      customerService,
		aiService:            aiService,
		config:               config,
		wsConnections:        make(map[string]*websocket.Conn),
		wsCleanupTicker:      time.NewTicker(30 * time.Second), // Cleanup every 30 seconds
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins in development
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 🧹 Start WebSocket cleanup routine
	go octopus.startWebSocketCleanup()

	octopus.setupHTTPServer()
	return octopus
}

// 🧹 WebSocket cleanup routine to prevent memory leaks
func (o *MorphicOctopusInterface) startWebSocketCleanup() {
	for range o.wsCleanupTicker.C {
		o.cleanupDeadConnections()
	}
}

// 🔍 Clean up dead WebSocket connections
func (o *MorphicOctopusInterface) cleanupDeadConnections() {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	deadConnections := make([]string, 0)

	for connID, conn := range o.wsConnections {
		// Send ping to check if connection is alive
		if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
			deadConnections = append(deadConnections, connID)
			conn.Close()
		}
	}

	// Remove dead connections
	for _, connID := range deadConnections {
		delete(o.wsConnections, connID)
		o.log.Infof("🧹 Cleaned up dead WebSocket connection: %s", connID)
	}

	if len(deadConnections) > 0 {
		o.log.Infof("🧹 Cleaned up %d dead WebSocket connections", len(deadConnections))
	}
}

// 🔌 Add WebSocket connection with thread safety
func (o *MorphicOctopusInterface) addWebSocketConnection(connID string, conn *websocket.Conn) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	o.wsConnections[connID] = conn
	o.log.Infof("🔌 Added WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
}

// 🔌 Remove WebSocket connection with thread safety
func (o *MorphicOctopusInterface) removeWebSocketConnection(connID string) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	if conn, exists := o.wsConnections[connID]; exists {
		conn.Close()
		delete(o.wsConnections, connID)
		o.log.Infof("🔌 Removed WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
	}
}

// 📊 Get WebSocket connection count
func (o *MorphicOctopusInterface) getWebSocketConnectionCount() int {
	o.wsConnectionsMutex.RLock()
	defer o.wsConnectionsMutex.RUnlock()

	return len(o.wsConnections)
}