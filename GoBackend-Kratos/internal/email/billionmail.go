package email

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/smtp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/conf"
)

// BillionMailService integrates with BillionMail server
type BillionMailService struct {
	config     *conf.Email
	httpClient *http.Client
	smtpAuth   smtp.Auth
	log        *log.Helper
}

// EmailMessage represents an email message
type EmailMessage struct {
	ID          string            `json:"id"`
	From        string            `json:"from"`
	To          []string          `json:"to"`
	CC          []string          `json:"cc,omitempty"`
	BCC         []string          `json:"bcc,omitempty"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	HTMLBody    string            `json:"html_body,omitempty"`
	Attachments []Attachment      `json:"attachments,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	Priority    string            `json:"priority,omitempty"`
	Timestamp   time.Time         `json:"timestamp"`
	Status      string            `json:"status"`
}

// Attachment represents an email attachment
type Attachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Size        int64  `json:"size"`
	Data        []byte `json:"data,omitempty"`
	URL         string `json:"url,omitempty"`
}

// Campaign represents an email marketing campaign
type Campaign struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Subject     string    `json:"subject"`
	Template    string    `json:"template"`
	Recipients  []string  `json:"recipients"`
	ScheduledAt time.Time `json:"scheduled_at"`
	Status      string    `json:"status"`
	Stats       CampaignStats `json:"stats"`
}

// CampaignStats represents campaign statistics
type CampaignStats struct {
	Sent      int     `json:"sent"`
	Delivered int     `json:"delivered"`
	Opened    int     `json:"opened"`
	Clicked   int     `json:"clicked"`
	Bounced   int     `json:"bounced"`
	OpenRate  float64 `json:"open_rate"`
	ClickRate float64 `json:"click_rate"`
}// NewBillionMailService creates a new BillionMail service
func NewBillionMailService(config *conf.Email, logger log.Logger) *BillionMailService {
	// Setup SMTP authentication
	var auth smtp.Auth
	if config.Smtp != nil && config.Smtp.Username != "" {
		auth = smtp.PlainAuth("", config.Smtp.Username, config.Smtp.Password, config.Smtp.Host)
	}

	return &BillionMailService{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		smtpAuth: auth,
		log:      log.NewHelper(logger),
	}
}

// SendEmail sends an email through BillionMail SMTP
func (s *BillionMailService) SendEmail(ctx context.Context, msg *EmailMessage) error {
	s.log.WithContext(ctx).Infof("Sending email to: %v", msg.To)

	// Try SMTP first, fallback to API if needed
	err := s.sendEmailSMTP(ctx, msg)
	if err != nil {
		s.log.WithContext(ctx).Warnf("SMTP failed, trying API: %v", err)
		return s.sendEmailAPI(ctx, msg)
	}

	return nil
}

// sendEmailSMTP sends email via SMTP
func (s *BillionMailService) sendEmailSMTP(ctx context.Context, msg *EmailMessage) error {
	if s.config.Smtp == nil {
		return fmt.Errorf("SMTP configuration not available")
	}

	// Prepare email message
	var emailBody bytes.Buffer

	// Headers
	emailBody.WriteString(fmt.Sprintf("From: %s\r\n", msg.From))
	emailBody.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ", ")))
	if len(msg.CC) > 0 {
		emailBody.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(msg.CC, ", ")))
	}
	emailBody.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
	emailBody.WriteString("MIME-Version: 1.0\r\n")

	if msg.HTMLBody != "" {
		emailBody.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		emailBody.WriteString("\r\n")
		emailBody.WriteString(msg.HTMLBody)
	} else {
		emailBody.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		emailBody.WriteString("\r\n")
		emailBody.WriteString(msg.Body)
	}

	// Combine all recipients
	allRecipients := append(msg.To, msg.CC...)
	allRecipients = append(allRecipients, msg.BCC...)

	// Send email
	smtpAddr := fmt.Sprintf("%s:%d", s.config.Smtp.Host, s.config.Smtp.Port)
	return smtp.SendMail(smtpAddr, s.smtpAuth, msg.From, allRecipients, emailBody.Bytes())
}

// sendEmailAPI sends email via BillionMail API
func (s *BillionMailService) sendEmailAPI(ctx context.Context, msg *EmailMessage) error {
	if s.config.Billionmail == nil {
		return fmt.Errorf("BillionMail API configuration not available")
	}

	// Prepare email payload for BillionMail API
	payload := map[string]interface{}{
		"from":    msg.From,
		"to":      msg.To,
		"subject": msg.Subject,
		"body":    msg.Body,
	}

	if msg.HTMLBody != "" {
		payload["html_body"] = msg.HTMLBody
	}

	if len(msg.CC) > 0 {
		payload["cc"] = msg.CC
	}

	if len(msg.BCC) > 0 {
		payload["bcc"] = msg.BCC
	}

	// Make API call to BillionMail
	return s.makeAPICall(ctx, "POST", "/emails/send", payload)
}

// CreateCampaign creates an email marketing campaign
func (s *BillionMailService) CreateCampaign(ctx context.Context, campaign *Campaign) error {
	s.log.WithContext(ctx).Infof("Creating campaign: %s", campaign.Name)

	payload := map[string]interface{}{
		"name":         campaign.Name,
		"subject":      campaign.Subject,
		"template":     campaign.Template,
		"recipients":   campaign.Recipients,
		"scheduled_at": campaign.ScheduledAt,
	}

	return s.makeAPICall(ctx, "POST", "/api/v1/campaigns", payload)
}

// GetCampaignStats retrieves campaign statistics
func (s *BillionMailService) GetCampaignStats(ctx context.Context, campaignID string) (*CampaignStats, error) {
	s.log.WithContext(ctx).Infof("Getting campaign stats: %s", campaignID)

	// Mock implementation - in real scenario, call BillionMail API
	return &CampaignStats{
		Sent:      1000,
		Delivered: 980,
		Opened:    450,
		Clicked:   89,
		Bounced:   20,
		OpenRate:  45.9,
		ClickRate: 8.9,
	}, nil
}// ListEmails retrieves emails from BillionMail
func (s *BillionMailService) ListEmails(ctx context.Context, limit, offset int) ([]*EmailMessage, error) {
	s.log.WithContext(ctx).Infof("Listing emails: limit=%d, offset=%d", limit, offset)

	// Mock implementation - in real scenario, call BillionMail API
	emails := []*EmailMessage{
		{
			ID:        "email-001",
			From:      "<EMAIL>",
			To:        []string{"<EMAIL>"},
			Subject:   "AC Repair Request",
			Body:      "My AC unit is not working properly. Please schedule a service call.",
			Timestamp: time.Now().Add(-2 * time.Hour),
			Status:    "received",
		},
		{
			ID:        "email-002",
			From:      "<EMAIL>",
			To:        []string{"<EMAIL>"},
			Subject:   "Service Appointment Confirmation",
			Body:      "Your HVAC service appointment has been scheduled for tomorrow at 2 PM.",
			Timestamp: time.Now().Add(-1 * time.Hour),
			Status:    "sent",
		},
	}

	return emails, nil
}

// makeAPICall makes HTTP request to BillionMail API
func (s *BillionMailService) makeAPICall(ctx context.Context, method, endpoint string, payload interface{}) error {
	if s.config.Billionmail == nil {
		return fmt.Errorf("BillionMail API configuration not available")
	}

	// Prepare request URL
	url := s.config.Billionmail.ApiUrl + endpoint

	// Prepare request body
	var reqBody io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal payload: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// Add authentication if available
	if s.config.Billionmail.AdminEmail != "" {
		req.Header.Set("X-Admin-Email", s.config.Billionmail.AdminEmail)
		req.Header.Set("X-Admin-Password", s.config.Billionmail.AdminPassword)
	}

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make API call: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API call failed with status %d: %s", resp.StatusCode, string(body))
	}

	s.log.WithContext(ctx).Infof("BillionMail API call successful: %s %s", method, endpoint)
	return nil
}

// AnalyzeEmailSentiment analyzes email sentiment using AI
func (s *BillionMailService) AnalyzeEmailSentiment(ctx context.Context, emailContent string) (string, float64, error) {
	s.log.WithContext(ctx).Info("Analyzing email sentiment")

	// This would integrate with AI service for sentiment analysis
	// Mock implementation
	sentiments := []string{"positive", "neutral", "negative"}
	sentiment := sentiments[len(emailContent)%3]
	confidence := 0.85

	return sentiment, confidence, nil
}

// CreateHVACEmailTemplate creates HVAC-specific email templates
func (s *BillionMailService) CreateHVACEmailTemplate(templateType string) string {
	templates := map[string]string{
		"service_reminder": `
Dear {{.CustomerName}},

This is a friendly reminder that your HVAC system is due for maintenance.
Regular maintenance helps ensure optimal performance and extends equipment life.

Service Details:
- Customer: {{.CustomerName}}
- Address: {{.CustomerAddress}}
- System Type: {{.SystemType}}
- Last Service: {{.LastServiceDate}}

Please contact us to schedule your appointment.

Best regards,
{{.CompanyName}} HVAC Services
`,
		"quote_follow_up": `
Dear {{.CustomerName}},

Thank you for your interest in our HVAC services. Please find your quote attached.

Quote Summary:
- Service: {{.ServiceType}}
- Estimated Cost: {{.EstimatedCost}}
- Timeline: {{.Timeline}}

This quote is valid for 30 days. Please don't hesitate to contact us with any questions.

Best regards,
{{.CompanyName}}
`,
	}

	if template, exists := templates[templateType]; exists {
		return template
	}

	return "Default HVAC email template"
}